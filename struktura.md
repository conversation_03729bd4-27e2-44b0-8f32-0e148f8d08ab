├── [frontend [file]
├── @DON [dir]
│  └── Gobeklitepe [dir]
│     └── AnimeJS_Inspiration [dir]
├── =1.8.0 [file]
├── 50x.html [file]
├── agent-protocol [dir]
│  ├── api.html [file]
│  ├── client-python [dir]
│  │  ├── ap_client [dir]
│  │  ├── docs [dir]
│  │  ├── git_push.sh [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── setup.cfg [file]
│  │  ├── setup.py [file]
│  │  ├── test [dir]
│  │  ├── test-requirements.txt [file]
│  │  └── tox.ini [file]
│  ├── CONTRIBUTING.md [file]
│  ├── LICENSE [file]
│  ├── models [dir]
│  │  ├── __init__.py [file]
│  │  ├── run.py [file]
│  │  ├── store.py [file]
│  │  └── thread.py [file]
│  ├── openapi.json [file]
│  ├── README.md [file]
│  ├── server [dir]
│  │  ├── ap_server [dir]
│  │  └── pyproject.toml [file]
│  └── tooling [dir]
│     ├── Makefile [file]
│     └── pyproject.toml [file]
├── AGENT.md [file]
├── analytics-dashboard [dir]
│  └── README.md [file]
├── api [dir]
├── api-proxy [dir]
│  ├── Dockerfile [file]
│  ├── package.json [file]
│  └── server.js [file]
├── app [dir]
│  ├── components [dir]
│  │  ├── molecules [dir]
│  │  └── organisms [dir]
│  └── types [dir]
│     └── shared.ts [file]
├── apply_data_consistency_fixes.js [file]
├── apply_migrations.enhanced.js [file]
├── apply_migrations.js [file]
├── augment_bielik_config.json [file]
├── augment_bielik_integration.py [file]
├── augment-bielik-mcp-fixed.json [file]
├── augment-bielik-mcp-simple.json [file]
├── augment-bielik-mcp.json [file]
├── augment-chat-bielik.service [file]
├── augment-chat-integration [dir]
│  ├── api.js [file]
│  ├── augment_chat_integration.js [file]
│  ├── augment-plugin [dir]
│  │  ├── bielik-plugin.js [file]
│  │  ├── package.json [file]
│  │  └── README.md [file]
│  ├── bielik_mcp_client.js [file]
│  ├── bielik-integration.service [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── index.js [file]
│  ├── INSTALL.md [file]
│  ├── package.json [file]
│  ├── README.md [file]
│  ├── start.sh [file]
│  └── test.js [file]
├── augment-chat-mcpServers.json [file]
├── augment-mcp-server-config.json [file]
├── backend [dir]
│  ├── api [dir]
│  │  ├── api [dir]
│  │  ├── db [dir]
│  │  ├── dependencies [dir]
│  │  ├── docker-compose.yml [file]
│  │  ├── Dockerfile [file]
│  │  ├── docs [dir]
│  │  ├── main.py [file]
│  │  ├── monitoring [dir]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── run_scheduler.sh [file]
│  │  ├── run_worker.sh [file]
│  │  ├── run.sh [file]
│  │  ├── scripts [dir]
│  │  ├── services [dir]
│  │  ├── tests [dir]
│  │  ├── utils [dir]
│  │  └── workers [dir]
│  ├── database [dir]
│  │  ├── 004_create_memory_bank_tables.sql [file]
│  │  ├── 005_create_advanced_relationships.sql [file]
│  │  ├── 006_create_email_memory_bank.sql [file]
│  │  └── 007_create_calendar_attachments_integration.sql [file]
│  ├── email [dir]
│  │  ├── audio_email_processor.py [file]
│  │  ├── audio_transcription_service.py [file]
│  │  ├── audio-email-processor.service [file]
│  │  ├── audio-transcription-service.service [file]
│  │  ├── check-mailboxes.js [file]
│  │  ├── config.js [file]
│  │  ├── docker-compose.yml [file]
│  │  ├── Dockerfile [file]
│  │  ├── emailReceiver.js [file]
│  │  ├── emailSender.js [file]
│  │  ├── import_all_audio_emails.py [file]
│  │  ├── index.js [file]
│  │  ├── install_services.sh [file]
│  │  ├── migrations [dir]
│  │  ├── monitor_email_processing.sh [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── run_import.sh [file]
│  │  ├── run_migrations.sh [file]
│  │  ├── setup_audio_processing.sh [file]
│  │  ├── setup_complete.sh [file]
│  │  ├── start-email-integration.sh [file]
│  │  ├── supabaseClient.js [file]
│  │  ├── test-email-integration.js [file]
│  │  ├── test-supabase.js [file]
│  │  └── tests [dir]
│  ├── email-services [dir]
│  │  ├── check-mailboxes.js [file]
│  │  ├── config.js [file]
│  │  ├── email-storage [dir]
│  │  ├── emailReceiver.js [file]
│  │  ├── emailSender.js [file]
│  │  ├── index.js [file]
│  │  ├── migrations [dir]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── start-email-integration.sh [file]
│  │  ├── test-email-integration.js [file]
│  │  └── tests [dir]
│  ├── llm-services [dir]
│  │  ├── attachments [dir]
│  │  ├── config.json [file]
│  │  ├── docker-compose.yml [file]
│  │  ├── Dockerfile [file]
│  │  ├── email_analyzer.py [file]
│  │  ├── email_processor.py [file]
│  │  ├── lmstudio.md [file]
│  │  ├── models [dir]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── response_generator.py [file]
│  │  └── setup.sh [file]
│  ├── memory-bank [dir]
│  │  ├── activeContext.md [file]
│  │  ├── alembic-commands.sh [file]
│  │  ├── alembic.ini [file]
│  │  ├── analytics_implementation.md [file]
│  │  ├── config.py [file]
│  │  ├── data_management.md [file]
│  │  ├── db [dir]
│  │  ├── do dodania.md [file]
│  │  ├── email-integration-success.md [file]
│  │  ├── For_later.md [file]
│  │  ├── gsap-docs.md [file]
│  │  ├── llm_agents.md [file]
│  │  ├── market_disruption_strategy.md [file]
│  │  ├── mobile_gsap_plan.md [file]
│  │  ├── musk_vision_implementation.md [file]
│  │  ├── plan.md [file]
│  │  ├── porady_react [file]
│  │  ├── porady_react_extended.md [file]
│  │  ├── productContext.md [file]
│  │  ├── Profound.md [file]
│  │  ├── progress.md [file]
│  │  ├── projectbrief.md [file]
│  │  ├── react_hooks_best_practices.md [file]
│  │  ├── README.md [file]
│  │  ├── run-migrations.sh [file]
│  │  ├── services [dir]
│  │  ├── systemPatterns.md [file]
│  │  ├── tech_innovation_roadmap.md [file]
│  │  ├── techContext.md [file]
│  │  ├── TO-DO [dir]
│  │  ├── utils [dir]
│  │  └── zakres_funkcjonalny.md [file]
│  ├── README.md [file]
│  ├── services [dir]
│  └── utils [dir]
│     └── supabase_client.py [file]
├── backup-supabase-enhanced.sh [file]
├── backup-supabase.sh [file]
├── backups [dir]
│  ├── ******** [dir]
│  │  ├── db [dir]
│  │  └── storage [dir]
│  ├── ********-035627 [dir]
│  │  ├── database.sql [file]
│  │  └── nginx-html [dir]
│  ├── ********-035647 [dir]
│  │  ├── database.sql [file]
│  │  └── nginx-html [dir]
│  ├── 20250510_033832 [dir]
│  │  ├── db [dir]
│  │  └── storage [dir]
│  ├── hvac-ui-********-154122 [dir]
│  ├── hvac-ui-********-154203 [dir]
│  ├── hvac-ui-********-164518 [dir]
│  ├── hvac-ui-********-164537 [dir]
│  ├── memory_bank_********_090251 [dir]
│  │  └── pre_migration_backup.dump [file]
│  ├── memory_bank_********_090730 [dir]
│  │  └── pre_migration_backup.dump [file]
│  ├── memory_bank_********_090804 [dir]
│  │  └── pre_migration_backup.dump [file]
│  ├── memory_bank_********_090923 [dir]
│  │  └── pre_migration_backup.dump [file]
│  ├── memory_bank_********_091005 [dir]
│  │  └── database_summary.txt [file]
│  ├── nginx-conf-********-164601 [dir]
│  │  └── default.conf.bak [file]
│  ├── supabase_backup_20250519_020001.tar.gz [file]
│  ├── supabase_backup_20250520_020001.tar.gz [file]
│  ├── supabase_backup_20250521_020001.tar.gz [file]
│  ├── supabase_backup_20250522_020001.tar.gz [file]
│  ├── supabase_backup_20250523_020001.tar.gz [file]
│  └── supabase_backup_20250524_020001.tar.gz [file]
├── bielik [dir]
│  ├── client [dir]
│  │  ├── bielik_mcp_client.js [file]
│  │  └── package.json [file]
│  ├── server [dir]
│  │  ├── Dockerfile [file]
│  │  ├── health_check.py [file]
│  │  └── requirements.txt [file]
│  └── trinity [dir]
├── bielik_mcp_client.py [file]
├── BIELIK_MCP_README.md [file]
├── bielik-integration [dir]
│  ├── api.js [file]
│  ├── bielik_integration.js [file]
│  ├── client [dir]
│  │  ├── augment_chat_integration.js [file]
│  │  └── bielik_mcp_client.js [file]
│  ├── core [dir]
│  │  ├── bielik_code_generator.js [file]
│  │  ├── bielik_connector.js [file]
│  │  └── sandbox_executor.js [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── index.js [file]
│  ├── memory [dir]
│  │  └── engram_stack.js [file]
│  ├── package.json [file]
│  ├── README.md [file]
│  ├── start.sh [file]
│  ├── test.js [file]
│  ├── tests [dir]
│  │  ├── simple_test.py [file]
│  │  └── test_bielik_mcp.py [file]
│  ├── ui [dir]
│  │  ├── hohelomat.js [file]
│  │  └── kwantowa_siec.js [file]
│  └── utils [dir]
│     └── config_loader.js [file]
├── bielik-mcp-docker [dir]
│  ├── augment-bielik-mcp-simple.json [file]
│  ├── augment-bielik-mcp.json [file]
│  ├── augment-integration [dir]
│  │  ├── augment_chat_integration.js [file]
│  │  ├── bielik_mcp_client.js [file]
│  │  ├── Dockerfile [file]
│  │  ├── index.js [file]
│  │  └── package.json [file]
│  ├── docker-compose.yml [file]
│  ├── prepare-code.sh [file]
│  ├── README.md [file]
│  ├── register-bielik-instances.sh [file]
│  ├── server [dir]
│  │  ├── core [dir]
│  │  ├── Dockerfile [file]
│  │  ├── integrations [dir]
│  │  └── requirements.txt [file]
│  ├── stop.sh [file]
│  ├── test-api.sh [file]
│  └── trinity [dir]
├── bielik-mcp.service [file]
├── bielik-podman [dir]
│  ├── augment-integration [dir]
│  │  ├── augment_chat_integration.js [file]
│  │  ├── bielik_mcp_client.js [file]
│  │  ├── Containerfile [file]
│  │  ├── index.js [file]
│  │  └── package.json [file]
│  ├── generate-kube-manifest.sh [file]
│  ├── migrate-from-docker.sh [file]
│  ├── migration-guide.md [file]
│  ├── podman-compose.yml [file]
│  ├── prepare-code-podman.sh [file]
│  ├── README.md [file]
│  ├── run-bielik-server.sh [file]
│  ├── run-containers-alt-ports.sh [file]
│  ├── run-containers-podman.sh [file]
│  ├── run-containers-simple.sh [file]
│  ├── run-with-podman-compose.sh [file]
│  ├── server [dir]
│  │  ├── Containerfile [file]
│  │  ├── core [dir]
│  │  ├── integrations [dir]
│  │  └── requirements.txt [file]
│  └── stop-containers.sh [file]
├── check_docker_files.sh [file]
├── check-calendar-tables.js [file]
├── check-supabase-health.sh [file]
├── Ciekawe [dir]
│  └── agent-squad [dir]
│     ├── CODE_OF_CONDUCT.md [file]
│     ├── CONTRIBUTING.md [file]
│     ├── docs [dir]
│     ├── examples [dir]
│     ├── img [dir]
│     ├── LICENSE [file]
│     ├── python [dir]
│     ├── README.md [file]
│     └── typescript [dir]
├── clean-augment-config.json [file]
├── config [dir]
│  ├── BIELIK_CONFIG_README.md [file]
│  ├── bielik_config.json [file]
│  ├── docs [dir]
│  │  ├── database.md [file]
│  │  ├── elevenlabs.md [file]
│  │  ├── emails.md [file]
│  │  └── planix.md [file]
│  ├── inbucket [dir]
│  │  └── greeting.html [file]
│  ├── mcp [dir]
│  │  └── gitmcp-config.json [file]
│  ├── nginx [dir]
│  │  ├── nginx-storage.conf [file]
│  │  └── nginx.conf [file]
│  ├── postgres [dir]
│  │  └── postgresql.conf [file]
│  ├── storage [dir]
│  │  ├── storage-service-config.yml [file]
│  │  ├── storage-service-updated.yml [file]
│  │  └── storage-service.yml [file]
│  └── supabase [dir]
│     ├── inbucket [dir]
│     ├── kong [dir]
│     ├── postgres [dir]
│     └── vector [dir]
├── Config_files [dir]
│  ├── database.md [file]
│  ├── elevenlabs.md [file]
│  ├── emails.md [file]
│  └── planix.md [file]
├── cooliber [file]
├── cooliber.pub [file]
├── Crawler mcp [dir]
│  ├── docker-compose.yml [file]
│  ├── integracja_crawler_mcp.md [file]
│  ├── mcp-crawl4ai-rag [dir]
│  │  ├── crawl4ai_mcp.py [file]
│  │  ├── crawled_pages.sql [file]
│  │  ├── Dockerfile [file]
│  │  ├── LICENSE [file]
│  │  ├── mcp-config.json [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  └── uv.lock [file]
│  ├── mcp-supabase-tools [dir]
│  │  ├── Dockerfile [file]
│  │  ├── mcp-config.json [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── setup.py [file]
│  │  └── src [dir]
│  ├── mcp-unified-tools [dir]
│  │  ├── Dockerfile [file]
│  │  ├── mcp-config.json [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── setup.py [file]
│  │  └── src [dir]
│  ├── README.md [file]
│  ├── test-mcp-services.sh [file]
│  └── toolshowto.md [file]
├── create_helper_functions.sql [file]
├── customer-portal [dir]
│  └── README.md [file]
├── data [dir]
│  ├── migration [dir]
│  │  ├── backups [dir]
│  │  ├── check-schema.js [file]
│  │  ├── config.js [file]
│  │  ├── database.js [file]
│  │  ├── enhanced-database.js [file]
│  │  ├── enhanced-migration.js [file]
│  │  ├── enhanced-verify.js [file]
│  │  ├── index.js [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── run-enhanced-migration.sh [file]
│  │  ├── run-migration.sh [file]
│  │  ├── ui-config-summary.json [file]
│  │  ├── update-ui-config.js [file]
│  │  ├── verify-data.js [file]
│  │  └── volumes [dir]
│  ├── operators [dir]
│  └── to-ingest [dir]
│     ├── calendar_archive.csv [file]
│     ├── clients_export.csv [file]
│     ├── clients_test.csv [file]
│     ├── database_schemas.json [file]
│     ├── eksport fv 2024-2025.04.xml [file]
│     ├── Eksportowanie dokumentów.csv [file]
│     └── Kartoteka kontrahentów_extracted.csv [file]
├── Data_to_ingest [dir]
│  ├── calendar_archive.csv [file]
│  ├── clients_export.csv [file]
│  ├── clients_test.csv [file]
│  ├── create_memory_bank_tables.sh [file]
│  ├── create_memory_bank_tables.sql [file]
│  ├── create_tables.sql [file]
│  ├── database_schemas.json [file]
│  ├── eksport fv 2024-2025.04.xml [file]
│  ├── Eksportowanie dokumentów.csv [file]
│  ├── import_data.py [file]
│  ├── Kartoteka kontrahentów_extracted.csv [file]
│  └── run_import.sh [file]
├── data-collection [dir]
│  ├── cli.py [file]
│  ├── collectors [dir]
│  │  └── email_collector.py [file]
│  ├── config.py [file]
│  ├── data_collector.py [file]
│  ├── enrichers [dir]
│  │  ├── address_geocoder.py [file]
│  │  └── customer_enricher.py [file]
│  ├── memory-bank.md [file]
│  ├── processors [dir]
│  │  └── email_processor.py [file]
│  ├── README.md [file]
│  ├── requirements.txt [file]
│  ├── run.sh [file]
│  ├── storage [dir]
│  │  └── supabase_storage.py [file]
│  └── validators [dir]
│     ├── address_validator.py [file]
│     └── customer_validator.py [file]
├── data-migration [dir]
│  ├── backups [dir]
│  │  ├── ********_043131 [dir]
│  │  ├── ********_043346 [dir]
│  │  ├── ********_043414 [dir]
│  │  ├── ********_043458 [dir]
│  │  ├── ********_043602 [dir]
│  │  ├── ********_083746 [dir]
│  │  ├── ********_083812 [dir]
│  │  ├── ********_083838 [dir]
│  │  ├── ********_083906 [dir]
│  │  └── ********_083921 [dir]
│  ├── check-schema.js [file]
│  ├── config.js [file]
│  ├── database.js [file]
│  ├── enhanced-database.js [file]
│  ├── enhanced-migration.js [file]
│  ├── enhanced-verify.js [file]
│  ├── index.js [file]
│  ├── package.json [file]
│  ├── README.md [file]
│  ├── run-enhanced-migration.sh [file]
│  ├── run-migration.sh [file]
│  ├── ui-config-summary.json [file]
│  ├── update-ui-config.js [file]
│  ├── verify-data.js [file]
│  └── volumes [dir]
│     ├── api [dir]
│     ├── db [dir]
│     └── storage [dir]
├── Data-operators [dir]
├── database [dir]
│  ├── alembic [dir]
│  │  ├── alembic.ini [file]
│  │  └── migrations [dir]
│  ├── migrations [dir]
│  │  ├── 007_create_audio_recordings.sql [file]
│  │  ├── 20230701_add_performance_indexes.sql [file]
│  │  ├── 20230702_create_reporting_views.sql [file]
│  │  ├── 20230703_create_memory_functions.sql [file]
│  │  └── 20240601_optimize_database.sql [file]
│  ├── README.md [file]
│  ├── run_migrations.sh [file]
│  └── sql [dir]
│     ├── count_all_records.sql [file]
│     ├── count_records.sql [file]
│     ├── fix-storage-migrations-v2.sql [file]
│     ├── fix-storage-migrations-v3.sql [file]
│     ├── fix-storage-migrations.sql [file]
│     └── fix-supabase-storage.sql [file]
├── daytona [dir]
│  ├── api [dir]
│  │  ├── daytona_api.py [file]
│  │  ├── Dockerfile [file]
│  │  ├── requirements.txt [file]
│  │  └── server.py [file]
│  ├── atomic_design.md [file]
│  ├── config [dir]
│  ├── daytona.service [file]
│  ├── docker-compose.yml [file]
│  ├── docs [dir]
│  ├── DOCUMENTATION.md [file]
│  ├── examples [dir]
│  │  ├── hvac_data_processing.py [file]
│  │  ├── hvac_example.py [file]
│  │  ├── README.md [file]
│  │  ├── test_bielik_integration.py [file]
│  │  ├── test_crawler_integration.py [file]
│  │  ├── test_daytona_api.py [file]
│  │  ├── test_dependency_installation.py [file]
│  │  ├── test_error_handling.py [file]
│  │  ├── test_gsap_animation.py [file]
│  │  ├── test_sandbox_creation.py [file]
│  │  ├── test_sandbox_restrictions.py [file]
│  │  ├── test_shell_script.py [file]
│  │  └── test_workspace_interaction.py [file]
│  ├── gsap-visualizer [dir]
│  │  ├── components [dir]
│  │  ├── css [dir]
│  │  ├── index.html [file]
│  │  ├── js [dir]
│  │  ├── README.md [file]
│  │  ├── run_visualizer.py [file]
│  │  ├── run_visualizer.sh [file]
│  │  ├── server.js [file]
│  │  └── visualizer.js [file]
│  ├── install_daytona.sh [file]
│  ├── install_service.sh [file]
│  ├── integrations [dir]
│  │  ├── bielik [dir]
│  │  └── crawler [dir]
│  ├── interfejs_uzytkownika.md [file]
│  ├── project [dir]
│  │  └── daytona.yaml [file]
│  ├── raport_implementacji_technicznej.md [file]
│  ├── raport_iso_zgodnosc.md [file]
│  ├── raport_przypadki_uzycia.md [file]
│  ├── raport_rozwoju.md [file]
│  ├── raport_zastosowania.md [file]
│  ├── README.md [file]
│  ├── runtime [dir]
│  │  ├── Dockerfile [file]
│  │  ├── requirements.txt [file]
│  │  └── server.py [file]
│  ├── start_daytona.sh [file]
│  ├── stop_daytona.sh [file]
│  ├── synteza_projektu.md [file]
│  ├── test_daytona.sh [file]
│  └── tests [dir]
│     ├── explore_api.py [file]
│     ├── explore_local_api.py [file]
│     ├── run_all_tests.sh [file]
│     ├── test_api.py [file]
│     ├── test_bielik_integration.py [file]
│     ├── test_crawler_integration.py [file]
│     ├── test_direct_api.py [file]
│     └── test_online_api.py [file]
├── debug_bielik_mcp.py [file]
├── deploy-hvac-ui.sh [file]
├── deploy-production.sh [file]
├── DEPLOYMENT.md [file]
├── DesktopCommanderMCP [dir]
├── dify [dir]
│  ├── agent-inbox [dir]
│  │  ├── components.json [file]
│  │  ├── LICENSE [file]
│  │  ├── next.config.mjs [file]
│  │  ├── package.json [file]
│  │  ├── postcss.config.mjs [file]
│  │  ├── public [dir]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  ├── tailwind.config.ts [file]
│  │  └── tsconfig.json [file]
│  ├── agent-protocol [dir]
│  │  ├── api.html [file]
│  │  ├── client-python [dir]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── LICENSE [file]
│  │  ├── openapi.json [file]
│  │  ├── README.md [file]
│  │  ├── server [dir]
│  │  └── tooling [dir]
│  ├── agentevals [dir]
│  │  ├── js [dir]
│  │  ├── LICENSE [file]
│  │  ├── python [dir]
│  │  ├── README.md [file]
│  │  ├── scripts [dir]
│  │  ├── static [dir]
│  │  └── uv.lock [file]
│  ├── agents-from-scratch-ts [dir]
│  │  ├── eslint.config.js [file]
│  │  ├── jest.config.mjs [file]
│  │  ├── langgraph.json [file]
│  │  ├── notebooks [dir]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  ├── tests [dir]
│  │  └── tsconfig.json [file]
│  ├── executive-ai-assistant [dir]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── eaia [dir]
│  │  ├── langgraph.json [file]
│  │  ├── LICENSE [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  └── scripts [dir]
│  ├── langchain-extract [dir]
│  │  ├── backend [dir]
│  │  ├── docker-compose.yml [file]
│  │  ├── Dockerfile [file]
│  │  ├── docs [dir]
│  │  ├── frontend [dir]
│  │  ├── LICENSE [file]
│  │  └── README.md [file]
│  ├── langchain-mcp-adapters [dir]
│  │  ├── examples [dir]
│  │  ├── langchain_mcp_adapters [dir]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── static [dir]
│  │  ├── tests [dir]
│  │  └── uv.lock [file]
│  ├── langchain-redis [dir]
│  │  ├── libs [dir]
│  │  ├── LICENSE [file]
│  │  └── README.md [file]
│  ├── langgraph-messaging-integrations [dir]
│  │  ├── langgraph.json [file]
│  │  ├── LICENSE [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  └── uv.lock [file]
│  ├── langgraph-supervisor-py [dir]
│  │  ├── langgraph_supervisor [dir]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── static [dir]
│  │  ├── tests [dir]
│  │  └── uv.lock [file]
│  ├── langgraph-swarm-py [dir]
│  │  ├── examples [dir]
│  │  ├── langgraph_swarm [dir]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── static [dir]
│  │  ├── tests [dir]
│  │  └── uv.lock [file]
│  ├── langgraphjs [dir]
│  │  ├── _scripts [dir]
│  │  ├── CLAUDE.md [file]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── deno.json [file]
│  │  ├── docs [dir]
│  │  ├── examples [dir]
│  │  ├── libs [dir]
│  │  ├── LICENSE [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── scripts [dir]
│  │  ├── tsconfig.json [file]
│  │  └── turbo.json [file]
│  ├── langgraphjs-api [dir]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── libs [dir]
│  │  ├── LICENSE [file]
│  │  ├── package.json [file]
│  │  ├── pnpm-workspace.yaml [file]
│  │  ├── README.md [file]
│  │  └── turbo.json [file]
│  ├── langgraphjs-gen-ui-examples [dir]
│  │  ├── components.json [file]
│  │  ├── eslint.config.js [file]
│  │  ├── index.html [file]
│  │  ├── langgraph.json [file]
│  │  ├── LICENSE [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  ├── static [dir]
│  │  ├── tailwind.config.js [file]
│  │  ├── tsconfig.json [file]
│  │  ├── tsconfig.node.json [file]
│  │  └── vite.config.ts [file]
│  ├── langmem [dir]
│  │  ├── docs [dir]
│  │  ├── examples [dir]
│  │  ├── langgraph.json [file]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── pyproject.toml [file]
│  │  ├── pytest.ini [file]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  ├── tests [dir]
│  │  └── uv.lock [file]
│  ├── long_term_memory_course [dir]
│  │  ├── notebooks [dir]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  └── src [dir]
│  ├── open-agent-platform [dir]
│  │  ├── apps [dir]
│  │  ├── CONCEPTS.md [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── tsconfig.json [file]
│  │  └── turbo.json [file]
│  ├── pomysł_na_siebie.md [file]
│  └── social-media-agent [dir]
│     ├── FEATURES.md [file]
│     ├── jest.config.js [file]
│     ├── jest.setup.cjs [file]
│     ├── langgraph.json [file]
│     ├── LICENSE [file]
│     ├── memory-v2 [dir]
│     ├── package.json [file]
│     ├── pyproject.toml [file]
│     ├── README.md [file]
│     ├── scripts [dir]
│     ├── slack-messaging [dir]
│     ├── src [dir]
│     ├── static [dir]
│     ├── tsconfig.json [file]
│     └── uv.lock [file]
├── direct_sql.js [file]
├── docker [dir]
│  ├── storage [dir]
│  │  ├── Dockerfile.storage [file]
│  │  ├── Dockerfile.storage-final [file]
│  │  ├── Dockerfile.storage-no-migrations [file]
│  │  ├── Dockerfile.storage-v2 [file]
│  │  ├── Dockerfile.storage-v3 [file]
│  │  └── Dockerfile.storage-v4 [file]
│  └── supabase [dir]
│     ├── docker-compose.supabase.clean.yml [file]
│     └── docker-compose.supabase.yml [file]
├── DOCKER_SETUP_GUIDE.md [file]
├── DOCKER_SETUP.md [file]
├── docker-compose.hvac-ui.yml [file]
├── docker-compose.prod.yml [file]
├── docker-compose.supabase.enhanced.yml [file]
├── docker-compose.supabase.full.yml [file]
├── docker-compose.supabase.new.yml [file]
├── docker-compose.supabase.simple.yml [file]
├── docker-compose.supabase.updated.yml [file]
├── docker-compose.supabase.yml [file]
├── docker-compose.supabase.yml.new [file]
├── docker-compose.yml [file]
├── docker-manager.sh [file]
├── docs [dir]
│  ├── calendar [dir]
│  │  └── supabase-calendar-integration.md [file]
│  ├── database [dir]
│  │  ├── database_diagram.md [file]
│  │  ├── database_documentation.md [file]
│  │  ├── database_migration_guide.md [file]
│  │  ├── database_optimization_guide.md [file]
│  │  ├── database_schema.md [file]
│  │  ├── database_summary.md [file]
│  │  ├── database-migration-strategy.md [file]
│  │  └── README.md [file]
│  ├── device_catalog_integration.md [file]
│  ├── executive_dashboard_implementation.md [file]
│  ├── executive_dashboard_plan.md [file]
│  ├── general [dir]
│  │  ├── CONFIG-MANAGEMENT.md [file]
│  │  ├── FULL_POTENTIAL_PLAN.md [file]
│  │  ├── memory_bank_summary.md [file]
│  │  ├── STRUCTURE.md [file]
│  │  └── VSCODE_MCP_SERVER.md [file]
│  ├── gitmcp_daytona_integration.md [file]
│  ├── hvac_api_architecture.mdl [file]
│  ├── implementation_progress.md [file]
│  ├── integration_architecture.md [file]
│  ├── mcp [dir]
│  │  └── standardized-mcp-tools.md [file]
│  ├── mdl [dir]
│  │  ├── atomic_design_principles.mdl [file]
│  │  ├── gsap_atomic_animations.mdl [file]
│  │  └── trinity_pattern.mdl [file]
│  ├── memory-bank [dir]
│  │  ├── activeContext.md [file]
│  │  ├── analytics_implementation.md [file]
│  │  ├── data_management.md [file]
│  │  ├── email-integration-success.md [file]
│  │  ├── gsap-docs.md [file]
│  │  ├── llm_agents.md [file]
│  │  ├── market_disruption_strategy.md [file]
│  │  ├── mobile_gsap_plan.md [file]
│  │  ├── musk_vision_implementation.md [file]
│  │  ├── plan.md [file]
│  │  ├── porady_react_extended.md [file]
│  │  ├── productContext.md [file]
│  │  ├── Profound.md [file]
│  │  ├── progress.md [file]
│  │  ├── projectbrief.md [file]
│  │  ├── react_hooks_best_practices.md [file]
│  │  ├── README.md [file]
│  │  ├── systemPatterns.md [file]
│  │  ├── tech_innovation_roadmap.md [file]
│  │  ├── techContext.md [file]
│  │  └── zakres_funkcjonalny.md [file]
│  ├── README.md [file]
│  └── supabase [dir]
│     ├── SUPABASE_README.md [file]
│     └── supabase-maintenance-guide.md [file]
├── DON [dir]
│  └── Gobeklitepe [dir]
│     ├── 13.md [file]
│     ├── double_twistor_prototype_story.md [file]
│     ├── drobne.md.md [file]
│     ├── prototype_activation_subspace.md [file]
│     ├── prototype_attention_head_mechanics.md [file]
│     ├── prototype_decoherence_mitigation.md [file]
│     ├── prototype_engrams.md [file]
│     ├── prototype_organic_cell_metaphor.md [file]
│     ├── prototype_phase_coherence.md [file]
│     ├── prototype_quantum_interpretation.md [file]
│     ├── prototype_quantum_seeding_harvest.md [file]
│     ├── prototype_trinity_dynamic_interface.md [file]
│     ├── prototype_trinity_quantum_interface_gsap.md [file]
│     ├── prototypy [dir]
│     ├── quantum_flow_137_transcendence.mdl [file]
│     ├── quantum_trinity_database_prototype.md [file]
│     ├── Świetliste Idee [dir]
│     └── tru137up.md [file]
├── email_semantic_tests_README.md [file]
├── email-integration [dir]
│  ├── agent_protocol_integration.py [file]
│  ├── audio_email_processor.py [file]
│  ├── audio_transcription_service.py [file]
│  ├── audio-email-processor.service [file]
│  ├── audio-transcription-service.service [file]
│  ├── babel.config.js [file]
│  ├── check-mailboxes.js [file]
│  ├── config.js [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── emailReceiver.js [file]
│  ├── emailSender.js [file]
│  ├── executive_assistant_integration.py [file]
│  ├── import_all_audio_emails.py [file]
│  ├── index.js [file]
│  ├── install_services.sh [file]
│  ├── invoice_extractor.py [file]
│  ├── migrations [dir]
│  │  ├── add_account_name_column.sql [file]
│  │  └── create_audio_recordings_table.sql [file]
│  ├── monitor_email_processing.sh [file]
│  ├── package.json [file]
│  ├── README.md [file]
│  ├── run_import.sh [file]
│  ├── run_migrations.sh [file]
│  ├── setup_audio_processing.sh [file]
│  ├── setup_complete.sh [file]
│  ├── start-email-integration.sh [file]
│  ├── supabaseClient.js [file]
│  ├── test-email-integration.js [file]
│  ├── test-supabase.js [file]
│  └── tests [dir]
│     └── test_email_receiver.js [file]
├── email-storage [dir]
│  ├── attachments [dir]
│  ├── fix_email_thread_function.sql [file]
│  ├── import_sample_emails.py [file]
│  └── inbox [dir]
├── ERROR [file]
├── eslint.config.js [file]
├── execute_sql.js [file]
├── executive-ai-assistant [dir]
│  ├── CONTRIBUTING.md [file]
│  ├── eaia [dir]
│  │  ├── __init__.py [file]
│  │  ├── cron_graph.py [file]
│  │  ├── gmail.py [file]
│  │  ├── main [dir]
│  │  ├── reflection_graphs.py [file]
│  │  └── schemas.py [file]
│  ├── langgraph.json [file]
│  ├── LICENSE [file]
│  ├── pyproject.toml [file]
│  ├── README.md [file]
│  └── scripts [dir]
│     ├── run_ingest.py [file]
│     ├── run_single.py [file]
│     ├── setup_cron.py [file]
│     └── setup_gmail.py [file]
├── find_supabase_config.js [file]
├── fix_bielik_mcp_final.sh [file]
├── fix_bielik_mcp_integration.py [file]
├── fix_bielik_mcp.py [file]
├── fix_supabase_users_comprehensive.sql [file]
├── fix_supabase_users.sql [file]
├── frontend [dir]
│  ├── customer-portal [dir]
│  │  └── README.md [file]
│  ├── hvac-ui [dir]
│  │  ├── components [dir]
│  │  ├── docs [dir]
│  │  ├── next-sitemap.config.js [file]
│  │  ├── next.config.js [file]
│  │  ├── package.json [file]
│  │  ├── public [dir]
│  │  ├── README.md [file]
│  │  ├── scripts [dir]
│  │  ├── src [dir]
│  │  ├── tsconfig.json [file]
│  │  └── utils [dir]
│  └── src [dir]
│     └── components [dir]
├── git-mcp [dir]
│  ├── app [dir]
│  │  ├── app.css [file]
│  │  ├── chat [dir]
│  │  ├── components [dir]
│  │  ├── entry.server.tsx [file]
│  │  ├── globals.css [file]
│  │  └── root.tsx [file]
│  ├── biome.json [file]
│  ├── components.json [file]
│  ├── img [dir]
│  │  ├── available-tools.png [file]
│  │  ├── claude-does-math-the-fancy-way.png [file]
│  │  ├── cover.png [file]
│  │  ├── GitMCP_final.mp4 [file]
│  │  ├── GitMCP_PW.mp4 [file]
│  │  ├── icon_black_cropped.png [file]
│  │  ├── icon_black.png [file]
│  │  ├── icon_cropped.png [file]
│  │  ├── icon.png [file]
│  │  ├── mcp-inspector-oauth-success.png [file]
│  │  ├── mcp-inspector-sse-config.png [file]
│  │  ├── mcp-inspector-successful-tool-call.png [file]
│  │  ├── mcp-login.png [file]
│  │  └── OG.png [file]
│  ├── LICENSE [file]
│  ├── package.json [file]
│  ├── playwright.config.ts [file]
│  ├── postcss.config.mjs [file]
│  ├── public [dir]
│  │  └── img [dir]
│  ├── README.md [file]
│  ├── SECURITY.md [file]
│  ├── src [dir]
│  │  ├── api [dir]
│  │  ├── index.ts [file]
│  │  ├── shared [dir]
│  │  ├── test [dir]
│  │  └── utils.ts [file]
│  ├── static [dir]
│  │  ├── cover.png [file]
│  │  ├── GitMCP_final.mp4 [file]
│  │  ├── GitMCP_PW.mp4 [file]
│  │  ├── icon_black_cropped.png [file]
│  │  ├── icon_black.png [file]
│  │  ├── icon_cropped.png [file]
│  │  ├── icon.png [file]
│  │  ├── img [file]
│  │  ├── OG.png [file]
│  │  └── README.md [file]
│  ├── tailwind.config.js [file]
│  ├── tests [dir]
│  │  ├── e2e [dir]
│  │  └── global-setup.ts [file]
│  ├── tsconfig.cloudflare.json [file]
│  ├── tsconfig.json [file]
│  ├── tsconfig.node.json [file]
│  ├── vite.config.ts [file]
│  ├── vitest.config.ts [file]
│  ├── worker-configuration.d.ts [file]
│  └── wrangler.jsonc [file]
├── hvac-api [dir]
│  ├── api [dir]
│  │  ├── __init__.py [file]
│  │  ├── dependencies.py [file]
│  │  └── main.py [file]
│  ├── db [dir]
│  │  ├── __init__.py [file]
│  │  ├── models.py [file]
│  │  ├── repositories [dir]
│  │  └── supabase.py [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── monitoring [dir]
│  │  ├── __init__.py [file]
│  │  ├── langsmith.py [file]
│  │  ├── logging_setup.py [file]
│  │  └── metrics.py [file]
│  ├── README.md [file]
│  ├── requirements.txt [file]
│  ├── run_scheduler.sh [file]
│  ├── run_worker.sh [file]
│  ├── run.sh [file]
│  ├── scripts [dir]
│  │  └── initialize_qdrant.py [file]
│  ├── services [dir]
│  │  ├── __init__.py [file]
│  │  ├── audio_processor.py [file]
│  │  ├── customer_matching [dir]
│  │  ├── customer_profile [dir]
│  │  ├── customers.py [file]
│  │  ├── device_service.py [file]
│  │  ├── email_processing_service.py [file]
│  │  ├── email_processor.py [file]
│  │  ├── entity_extraction [dir]
│  │  ├── inventory_service.py [file]
│  │  ├── invoice_service.py [file]
│  │  ├── llm [dir]
│  │  ├── memory [dir]
│  │  ├── memory_bank [dir]
│  │  ├── notification_service.py [file]
│  │  ├── orders.py [file]
│  │  ├── purchase_order_service.py [file]
│  │  ├── report_service.py [file]
│  │  ├── service_order_service.py [file]
│  │  └── technician_schedule_service.py [file]
│  ├── test_email_processing.py [file]
│  ├── tests [dir]
│  ├── utils [dir]
│  │  ├── __init__.py [file]
│  │  ├── config.py [file]
│  │  ├── helpers.py [file]
│  │  └── logging.py [file]
│  └── workers [dir]
│     ├── __init__.py [file]
│     ├── scheduler.py [file]
│     ├── tasks.py [file]
│     └── worker.py [file]
├── hvac-crm-system [dir]
│  ├── 50x.html [file]
│  ├── default.conf [file]
│  ├── docker-compose.yml [file]
│  ├── docs [dir]
│  │  ├── architecture.md [file]
│  │  ├── dev-guide.md [file]
│  │  ├── monitoring.md [file]
│  │  ├── runbook.md [file]
│  │  └── user-guide.md [file]
│  ├── hvac-crm-system [dir]
│  │  └── services [dir]
│  ├── init.sh [file]
│  ├── README.md [file]
│  ├── services [dir]
│  │  ├── api [dir]
│  │  ├── grafana [dir]
│  │  ├── nginx [dir]
│  │  ├── prometheus [dir]
│  │  └── worker [dir]
│  └── tests [dir]
│     └── test_notifications.py [file]
├── hvac-crm.service [file]
├── hvac-crm.sh [file]
├── hvac-remix [dir]
│  ├── app [dir]
│  │  ├── components [dir]
│  │  ├── contexts [dir]
│  │  ├── db.server.ts [file]
│  │  ├── entry.client.tsx [file]
│  │  ├── entry.server.tsx [file]
│  │  ├── entry.worker.ts [file]
│  │  ├── graphql [dir]
│  │  ├── hooks [dir]
│  │  ├── index.js [file]
│  │  ├── lib [dir]
│  │  ├── middleware [dir]
│  │  ├── models [dir]
│  │  ├── root.error.tsx [file]
│  │  ├── root.tsx [file]
│  │  ├── service-worker.ts [file]
│  │  ├── services [dir]
│  │  ├── session.server.ts [file]
│  │  ├── singleton.server.ts [file]
│  │  ├── supabase.server.ts [file]
│  │  ├── tailwind.css [file]
│  │  ├── types [dir]
│  │  ├── utils [dir]
│  │  ├── utils.extended.test.ts [file]
│  │  ├── utils.test.ts [file]
│  │  └── utils.ts [file]
│  ├── azure-pipelines.yml [file]
│  ├── cypress [dir]
│  │  ├── e2e [dir]
│  │  ├── fixtures [dir]
│  │  ├── README.md [file]
│  │  ├── support [dir]
│  │  └── tsconfig.json [file]
│  ├── cypress.config.ts [file]
│  ├── data [dir]
│  │  └── qdrant [dir]
│  ├── docker-compose.ci.yml [file]
│  ├── docker-compose.dev.yml [file]
│  ├── docker-compose.prod.yml [file]
│  ├── docker-compose.yml [file]
│  ├── docker-entrypoint.sh [file]
│  ├── Dockerfile [file]
│  ├── Dockerfile.custom [file]
│  ├── Dockerfile.dev [file]
│  ├── docs [dir]
│  │  ├── admin-user-guide.md [file]
│  │  ├── ambient-intelligence-crm-langchain-integration.md [file]
│  │  ├── ambient-intelligence-crm.md [file]
│  │  ├── api [dir]
│  │  ├── api-documentation.md [file]
│  │  ├── ci-cd-pipeline.md [file]
│  │  ├── contact-management-and-automation.md [file]
│  │  ├── contact-management-enhancements.md [file]
│  │  ├── data-visualization.md [file]
│  │  ├── deployment.md [file]
│  │  ├── disaster-recovery-procedures.md [file]
│  │  ├── field-technician-guide.md [file]
│  │  ├── ocr-implementation.md [file]
│  │  ├── personalization-system.md [file]
│  │  ├── production-optimization-implementation.md [file]
│  │  ├── production-optimization.md [file]
│  │  ├── production-readiness-implementation.md [file]
│  │  ├── README.md [file]
│  │  ├── role-based-access-control.md [file]
│  │  ├── role-based-navigation.md [file]
│  │  ├── technical [dir]
│  │  ├── technical-architecture.md [file]
│  │  └── user [dir]
│  ├── fly.toml [file]
│  ├── init-project.sh [file]
│  ├── LICENSE [file]
│  ├── LICENSE.md [file]
│  ├── mocks [dir]
│  │  ├── index.js [file]
│  │  └── README.md [file]
│  ├── nginx [dir]
│  │  ├── conf.d [dir]
│  │  └── nginx.conf [file]
│  ├── nginx.conf [file]
│  ├── package-alias.js [file]
│  ├── package.json [file]
│  ├── patch-imports.js [file]
│  ├── postcss.config.js [file]
│  ├── prettier.config.js [file]
│  ├── prisma [dir]
│  │  ├── data [dir]
│  │  ├── data.db [file]
│  │  ├── migrations [dir]
│  │  ├── schema.prisma [file]
│  │  ├── seed.ts [file]
│  │  └── sqlite-schema.prisma [file]
│  ├── public [dir]
│  │  ├── favicon.ico [file]
│  │  ├── manifest.json [file]
│  │  ├── robots.txt [file]
│  │  ├── service-worker.js [file]
│  │  ├── sitemap.xml [file]
│  │  └── uploads [dir]
│  ├── README.md [file]
│  ├── remix.config.js [file]
│  ├── remix.init [dir]
│  │  ├── gitignore [file]
│  │  ├── index.js [file]
│  │  └── package.json [file]
│  ├── remix.pwa.js [file]
│  ├── resolver.js [file]
│  ├── scripts [dir]
│  │  ├── backup-database.js [file]
│  │  ├── deploy-production.sh [file]
│  │  ├── deploy.sh [file]
│  │  ├── generate-docs.js [file]
│  │  ├── generate-migration-sql.js [file]
│  │  ├── generate-og-images.js [file]
│  │  ├── generate-secrets.js [file]
│  │  ├── load-testing.js [file]
│  │  ├── migrate-data-to-supabase.js [file]
│  │  ├── migrate-data.js [file]
│  │  ├── migrate-to-postgres.js [file]
│  │  ├── optimize-database.js [file]
│  │  ├── production-optimize.js [file]
│  │  ├── production-readiness [dir]
│  │  ├── production-readiness.js [file]
│  │  ├── push-schema.js [file]
│  │  ├── reset-supabase.js [file]
│  │  ├── security-check.js [file]
│  │  ├── setup-monitoring.js [file]
│  │  └── setup-supabase.js [file]
│  ├── start.sh [file]
│  ├── Status_todo [dir]
│  │  ├── 01_overview.md [file]
│  │  ├── 02_integration_roadmap.md [file]
│  │  ├── 03_benefits.md [file]
│  │  ├── 04_bielik_integration.md [file]
│  │  ├── 05_architecture.md [file]
│  │  ├── 06_summary.md [file]
│  │  ├── 07_ocr_implementation.md [file]
│  │  ├── 08_ocr_enhancements.md [file]
│  │  ├── 09_semantic_search.md [file]
│  │  ├── 10_calendar_enhancement.md [file]
│  │  ├── 12_calendar_semantic_analysis.md [file]
│  │  ├── 12_cypress_testing_implementation.md [file]
│  │  ├── 13_cypress_testing_summary.md [file]
│  │  ├── 14_multi_channel_notifications.md [file]
│  │  ├── 15_offline_capabilities.md [file]
│  │  ├── 16_automated_service_order_creation.md [file]
│  │  ├── 17_printable_reports_implementation.md [file]
│  │  ├── 18_client_offer_system_and_gsap_visualizations.md [file]
│  │  ├── 19_ambient_intelligence_crm.md [file]
│  │  ├── 20_ambient_intelligence_langchain.md [file]
│  │  ├── 21_multi_factor_authentication.md [file]
│  │  ├── 69enchants.md [file]
│  │  ├── biblioteki_do_wdrozenia.md [file]
│  │  ├── implementation_summary.md [file]
│  │  ├── offline_support_implementation.md [file]
│  │  ├── predictive_maintenance_implementation.md [file]
│  │  ├── README.md [file]
│  │  ├── steps.md [file]
│  │  ├── steps.md.bak [file]
│  │  ├── story.md [file]
│  │  ├── todo_updated.md [file]
│  │  ├── todo.md [file]
│  │  └── wzmocnienie.md [file]
│  ├── supabase [dir]
│  │  ├── config.toml [file]
│  │  ├── functions [dir]
│  │  ├── migrations [dir]
│  │  └── schema.sql [file]
│  ├── supabase_schema_part_aa [file]
│  ├── supabase_schema_part_ab [file]
│  ├── supabase_schema_part_ac [file]
│  ├── supabase_schema_part_ad [file]
│  ├── supabase_schema.sql [file]
│  ├── tailwind.config.ts [file]
│  ├── test [dir]
│  ├── tsconfig.json [file]
│  └── vitest.config.ts [file]
├── hvac-ui [dir]
│  ├── components [dir]
│  │  ├── ClientProfileForm.js [file]
│  │  ├── EmailProcessingComponent.tsx [file]
│  │  └── MemoryBankComponent.tsx [file]
│  ├── current_errors.md [file]
│  ├── dependency-update-notes.md [file]
│  ├── dependency-update-results.md [file]
│  ├── dependency-update-summary.md [file]
│  ├── docker-compose.prod.yml [file]
│  ├── docker-compose.simple.yml [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── Dockerfile.prod [file]
│  ├── Dockerfile.prod.simple [file]
│  ├── docs [dir]
│  │  ├── email-processing-integration.md [file]
│  │  ├── memory-bank-integration.md [file]
│  │  ├── microsoft-graph-integration.md [file]
│  │  ├── NEXT_STEPS.md [file]
│  │  ├── offline-mode.md [file]
│  │  ├── PRODUCTION_DEPLOYMENT.md [file]
│  │  ├── PRODUCTION_READINESS_REPORT.md [file]
│  │  ├── PRODUCTION_READINESS.md [file]
│  │  ├── production-readiness-analysis.md [file]
│  │  ├── production-readiness-checklist.md [file]
│  │  ├── production-readiness-status-update.md [file]
│  │  ├── production-readiness.md [file]
│  │  ├── status_todo.md [file]
│  │  └── typescript-fixes-progress.md [file]
│  ├── jest.config.js [file]
│  ├── jest.setup.js [file]
│  ├── next-sitemap.config.js [file]
│  ├── next.config.backup.js [file]
│  ├── next.config.js [file]
│  ├── next.config.static.js [file]
│  ├── nginx [dir]
│  │  ├── conf.d [dir]
│  │  ├── nginx.conf [file]
│  │  └── ssl [dir]
│  ├── package.json [file]
│  ├── package.json.backup [file]
│  ├── PRODUCTION.md [file]
│  ├── public [dir]
│  │  ├── 404.html [file]
│  │  ├── 50x.html [file]
│  │  ├── favicon.ico [file]
│  │  ├── icons [dir]
│  │  ├── images [dir]
│  │  ├── logo.png [file]
│  │  ├── manifest.json [file]
│  │  ├── offline.html [file]
│  │  ├── optimized [dir]
│  │  ├── static-api [dir]
│  │  ├── sw.js [file]
│  │  └── workbox-4754cb34.js [file]
│  ├── README.md [file]
│  ├── roadmap.md [file]
│  ├── scripts [dir]
│  │  ├── check-a11y.js [file]
│  │  ├── check-accessibility.js [file]
│  │  ├── check-browser-errors.js [file]
│  │  ├── check-hooks-usage.js [file]
│  │  ├── check-memory-leaks.js [file]
│  │  ├── check-production-readiness.js [file]
│  │  ├── check-security.js [file]
│  │  ├── check-types.js [file]
│  │  ├── consolidate-structure.js [file]
│  │  ├── create-supabase-tables.js [file]
│  │  ├── create-tables.sql [file]
│  │  ├── deploy-docker.sh [file]
│  │  ├── deploy-nginx.sh [file]
│  │  ├── deploy-production.js [file]
│  │  ├── disable-eslint-errors.js [file]
│  │  ├── fix-global-polyfill.js [file]
│  │  ├── fix-hooks-usage.js [file]
│  │  ├── fix-self-polyfill.js [file]
│  │  ├── generate-icons.js [file]
│  │  ├── generate-static-api.js [file]
│  │  ├── optimize-all.js [file]
│  │  ├── optimize-docker.js [file]
│  │  ├── optimize-images.js [file]
│  │  ├── optimize-mcp-servers.js [file]
│  │  ├── optimize-next-config.js [file]
│  │  ├── optimize-vps.sh [file]
│  │  ├── package.json [file]
│  │  ├── push-container.sh [file]
│  │  ├── security-check.js [file]
│  │  ├── setup-monitoring.js [file]
│  │  ├── simple-supabase-test.js [file]
│  │  ├── test-supabase-integration.js [file]
│  │  └── update-imports.js [file]
│  ├── server.js [file]
│  ├── src [dir]
│  │  ├── api [dir]
│  │  ├── components [dir]
│  │  ├── config [dir]
│  │  ├── context [dir]
│  │  ├── db [dir]
│  │  ├── hooks [dir]
│  │  ├── lib [dir]
│  │  ├── middleware.ts [file]
│  │  ├── monitoring [dir]
│  │  ├── pages [dir]
│  │  ├── polyfills [dir]
│  │  ├── providers [dir]
│  │  ├── services [dir]
│  │  ├── styles [dir]
│  │  ├── theme.ts [file]
│  │  ├── types [dir]
│  │  └── utils [dir]
│  ├── tsconfig.jest.json [file]
│  ├── tsconfig.json [file]
│  └── utils [dir]
│     ├── api-utils.ts [file]
│     ├── errorHandling.ts [file]
│     └── offlineStorage.ts [file]
├── improve_data_consistency.sql [file]
├── infrastructure [dir]
│  ├── crawler [dir]
│  │  ├── docker-compose.yml [file]
│  │  ├── mcp-crawl4ai-rag [dir]
│  │  ├── mcp-supabase-tools [dir]
│  │  ├── mcp-unified-tools [dir]
│  │  ├── README.md [file]
│  │  └── test-mcp-services.sh [file]
│  ├── deploy-production.sh [file]
│  ├── deploy-ui-production.sh [file]
│  ├── deploy.sh [file]
│  ├── docker [dir]
│  │  └── docker-compose.supabase.yml [file]
│  ├── hvac-crm.service [file]
│  ├── README.md [file]
│  └── supabase [dir]
│     ├── docker-compose.yml [file]
│     └── volumes [dir]
├── init_supabase_db.sql [file]
├── init_supabase.sh [file]
├── init-supabase-db.sh [file]
├── install-bielik-mcp.sh [file]
├── integrate-supabase.sh [file]
├── iot-integration [dir]
│  └── README.md [file]
├── klucz [file]
├── klucz.pub [file]
├── Kuchnia_agentów [dir]
│  └── dify [dir]
│     ├── api [dir]
│     ├── AUTHORS [file]
│     ├── CONTRIBUTING_CN.md [file]
│     ├── CONTRIBUTING_DE.md [file]
│     ├── CONTRIBUTING_ES.md [file]
│     ├── CONTRIBUTING_FR.md [file]
│     ├── CONTRIBUTING_JA.md [file]
│     ├── CONTRIBUTING_KR.md [file]
│     ├── CONTRIBUTING_PT.md [file]
│     ├── CONTRIBUTING_TR.md [file]
│     ├── CONTRIBUTING_TW.md [file]
│     ├── CONTRIBUTING_VI.md [file]
│     ├── CONTRIBUTING.md [file]
│     ├── dev [dir]
│     ├── docker [dir]
│     ├── images [dir]
│     ├── LICENSE [file]
│     ├── Makefile [file]
│     ├── README_AR.md [file]
│     ├── README_BN.md [file]
│     ├── README_CN.md [file]
│     ├── README_DE.md [file]
│     ├── README_ES.md [file]
│     ├── README_FR.md [file]
│     ├── README_JA.md [file]
│     ├── README_KL.md [file]
│     ├── README_KR.md [file]
│     ├── README_PT.md [file]
│     ├── README_SI.md [file]
│     ├── README_TR.md [file]
│     ├── README_TW.md [file]
│     ├── README_VI.md [file]
│     ├── README.md [file]
│     ├── sdks [dir]
│     └── web [dir]
├── langchain-automation [dir]
│  ├── agents [dir]
│  │  ├── customer_agent [dir]
│  │  ├── email_agent.py [file]
│  │  └── supervisor_integration.py [file]
│  ├── api [dir]
│  │  ├── config.py [file]
│  │  ├── main.py [file]
│  │  └── predictive_maintenance_api.py [file]
│  ├── ARCHITECTURE_DIAGRAM.md [file]
│  ├── bielik-orchestration-docs.md [file]
│  ├── core [dir]
│  │  ├── __init__.py [file]
│  │  ├── a2a [dir]
│  │  ├── agent_protocol [dir]
│  │  ├── ai [dir]
│  │  ├── calendar [dir]
│  │  ├── chains [dir]
│  │  ├── email_processing [dir]
│  │  ├── langgraph_integration [dir]
│  │  ├── llm [dir]
│  │  ├── nodes [dir]
│  │  ├── orchestration [dir]
│  │  ├── orchestrator.py [file]
│  │  ├── rag [dir]
│  │  ├── semantic_analyzer.py [file]
│  │  ├── trinity [dir]
│  │  ├── unified_analyzer.py [file]
│  │  └── utils [dir]
│  ├── cytolog prwdy.md [file]
│  ├── data [dir]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── docs [dir]
│  │  ├── a2a_protocol.mdl [file]
│  │  ├── bielik_instances_interface.mdl [file]
│  │  ├── bielik_langchain_integration.mdl [file]
│  │  ├── langchain_automation_system.mdl [file]
│  │  ├── langwatch_integration.mdl [file]
│  │  ├── multi_instance_orchestration.mdl [file]
│  │  ├── rag_system.mdl [file]
│  │  ├── supadantic_integration.mdl [file]
│  │  └── supadantic_rag_integration.mdl [file]
│  ├── examples [dir]
│  │  ├── __init__.py [file]
│  │  ├── bielik_function_calling_example.py [file]
│  │  ├── bielik_langgraph_example.py [file]
│  │  ├── bielik_rag_example.py [file]
│  │  ├── bielik_streaming_example.py [file]
│  │  ├── customer_support_example.py [file]
│  │  ├── email_processing_example.py [file]
│  │  ├── email_processing_graph.py [file]
│  │  ├── monitoring_example.py [file]
│  │  ├── process_real_email.py [file]
│  │  ├── supadantic_langchain_example.py [file]
│  │  ├── test_email_processing.py [file]
│  │  ├── trinity_example.py [file]
│  │  └── unified_analyzer_example.py [file]
│  ├── FUTURE_ROADMAP.md [file]
│  ├── health_checks [dir]
│  │  └── bielik_health_check.py [file]
│  ├── IMPLEMENTATION_SUCCESS.md [file]
│  ├── implementation-plan.md [file]
│  ├── inspiracje-wieloprzypnieniowe.md [file]
│  ├── install_dependencies.sh [file]
│  ├── integrations [dir]
│  │  ├── langwatch [dir]
│  │  ├── mcp [dir]
│  │  ├── memory_bank [dir]
│  │  ├── qdrant [dir]
│  │  └── supabase [dir]
│  ├── interesting_repo.md [file]
│  ├── memory_bank_implementation_completed.md [file]
│  ├── monitoring [dir]
│  │  ├── __init__.py [file]
│  │  └── langsmith_monitor.py [file]
│  ├── multi-orchestration-readme.md [file]
│  ├── phase1_implementation_summary.md [file]
│  ├── README.md [file]
│  ├── register-bielik-instances.sh [file]
│  ├── requirements.txt [file]
│  ├── run_all_examples.py [file]
│  ├── run_email_processing.py [file]
│  ├── run_langgraph_example.py [file]
│  ├── run_langsmith_example.py [file]
│  ├── run_server.sh [file]
│  ├── run_supadantic_example.sh [file]
│  ├── run_tests.sh [file]
│  ├── run_ui.sh [file]
│  ├── setup.py [file]
│  ├── seven_node_architecture_implementation_completed.md [file]
│  ├── seven_node_architecture_implementation_summary.md [file]
│  ├── simple-bielik-test.py [file]
│  ├── start-multi-orchestration.sh [file]
│  ├── start-system.sh [file]
│  ├── start-ui-dev.sh [file]
│  ├── test_bielik_basic.py [file]
│  ├── test_bielik_connection.py [file]
│  ├── test_bielik_curl.sh [file]
│  ├── test_bielik_direct.py [file]
│  ├── test_bielik_langchain_integration.py [file]
│  ├── test_bielik_simple_langchain.py [file]
│  ├── test_bielik_simple.py [file]
│  ├── test_email_openai.py [file]
│  ├── test_integration.py [file]
│  ├── test_langchain_bielik_integration.py [file]
│  ├── test_langchain_simple.py [file]
│  ├── test_openai_direct.py [file]
│  ├── test-bielik-orchestration.py [file]
│  ├── test-local-bielik.py [file]
│  ├── tests [dir]
│  │  ├── test_bielik_integration.py [file]
│  │  ├── test_customer_agent.py [file]
│  │  ├── test_email_agent.py [file]
│  │  ├── test_email_chain.py [file]
│  │  ├── test_fallback_node.py [file]
│  │  ├── test_guardrail_node.py [file]
│  │  ├── test_rag_chain.py [file]
│  │  ├── test_supadantic_integration.py [file]
│  │  └── test_user_input_node.py [file]
│  ├── ui [dir]
│  │  ├── accessibility.md [file]
│  │  ├── Dockerfile [file]
│  │  ├── nginx.conf [file]
│  │  ├── package.json [file]
│  │  ├── public [dir]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  └── start-dev.sh [file]
│  ├── ui_implementation_completed.md [file]
│  └── usagi.md [file]
├── langchain-automation copy [dir]
│  ├── agents [dir]
│  │  ├── customer_agent [dir]
│  │  └── email_agent.py [file]
│  ├── api [dir]
│  │  └── main.py [file]
│  ├── core [dir]
│  │  ├── a2a [dir]
│  │  ├── chains [dir]
│  │  ├── llm [dir]
│  │  ├── orchestration [dir]
│  │  ├── rag [dir]
│  │  └── utils [dir]
│  ├── cytolog prwdy.md [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── examples [dir]
│  │  ├── customer_support_example.py [file]
│  │  ├── email_processing_example.py [file]
│  │  └── monitoring_example.py [file]
│  ├── implementation-plan.md [file]
│  ├── inspiracje-wieloprzypnieniowe.md [file]
│  ├── integrations [dir]
│  │  ├── langwatch [dir]
│  │  ├── memory_bank [dir]
│  │  ├── qdrant [dir]
│  │  └── supabase [dir]
│  ├── interesting_repo.md [file]
│  ├── multi-orchestration-readme.md [file]
│  ├── README.md [file]
│  ├── requirements.txt [file]
│  ├── start-multi-orchestration.sh [file]
│  ├── start-system.sh [file]
│  ├── start-ui-dev.sh [file]
│  ├── tests [dir]
│  │  ├── test_customer_agent.py [file]
│  │  ├── test_email_agent.py [file]
│  │  ├── test_email_chain.py [file]
│  │  └── test_rag_chain.py [file]
│  ├── ui [dir]
│  │  ├── accessibility.md [file]
│  │  ├── Dockerfile [file]
│  │  ├── nginx.conf [file]
│  │  ├── package.json [file]
│  │  ├── public [dir]
│  │  ├── README.md [file]
│  │  ├── src [dir]
│  │  └── start-dev.sh [file]
│  ├── ui_implementation_completed.md [file]
│  └── usagi.md [file]
├── langchain-extract [dir]
│  ├── backend [dir]
│  │  ├── db [dir]
│  │  ├── Dockerfile [file]
│  │  ├── extraction [dir]
│  │  ├── Makefile [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── scripts [dir]
│  │  ├── server [dir]
│  │  └── tests [dir]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── docs [dir]
│  │  ├── make.bat [file]
│  │  ├── Makefile [file]
│  │  └── source [dir]
│  ├── frontend [dir]
│  │  ├── app [dir]
│  │  ├── Dockerfile [file]
│  │  ├── next.config.js [file]
│  │  ├── package.json [file]
│  │  ├── postcss.config.js [file]
│  │  ├── public [dir]
│  │  ├── tailwind.config.ts [file]
│  │  └── tsconfig.json [file]
│  ├── LICENSE [file]
│  └── README.md [file]
├── langchain-redis [dir]
│  ├── libs [dir]
│  │  └── redis [dir]
│  ├── LICENSE [file]
│  └── README.md [file]
├── langchain-unified [dir]
│  ├── api [dir]
│  │  ├── config.py [file]
│  │  └── main.py [file]
│  ├── core [dir]
│  │  ├── llm [dir]
│  │  ├── nodes [dir]
│  │  ├── orchestration [dir]
│  │  ├── trinity [dir]
│  │  └── utils [dir]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── docs [dir]
│  │  ├── a2a_protocol.mdl [file]
│  │  ├── bielik_instances_interface.mdl [file]
│  │  ├── bielik_langchain_integration.mdl [file]
│  │  ├── langchain_automation_system.mdl [file]
│  │  ├── langwatch_integration.mdl [file]
│  │  ├── multi_instance_orchestration.mdl [file]
│  │  ├── rag_system.mdl [file]
│  │  ├── supadantic_integration.mdl [file]
│  │  └── supadantic_rag_integration.mdl [file]
│  ├── install.sh [file]
│  ├── integrations [dir]
│  │  ├── bielik [dir]
│  │  ├── calendar [dir]
│  │  ├── email [dir]
│  │  ├── redis [dir]
│  │  └── supabase [dir]
│  ├── main.py [file]
│  ├── memory [dir]
│  │  ├── bank [dir]
│  │  ├── conversation [dir]
│  │  ├── docs [dir]
│  │  ├── examples [dir]
│  │  ├── langgraph.json [file]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── pyproject.toml [file]
│  │  ├── pytest.ini [file]
│  │  ├── README.md [file]
│  │  ├── semantic [dir]
│  │  ├── src [dir]
│  │  ├── tests [dir]
│  │  ├── uv.lock [file]
│  │  └── vector [dir]
│  ├── README.md [file]
│  ├── requirements.txt [file]
│  ├── run_server.sh [file]
│  ├── run_supadantic_example.sh [file]
│  ├── run_tests.sh [file]
│  ├── run_ui.sh [file]
│  ├── setup.py [file]
│  ├── start-multi-orchestration.sh [file]
│  ├── start-system.sh [file]
│  ├── start-ui-dev.sh [file]
│  ├── start.sh [file]
│  ├── SYNTHESIS_SUMMARY.md [file]
│  ├── tests [dir]
│  │  ├── test_bielik_integration.py [file]
│  │  ├── test_customer_agent.py [file]
│  │  ├── test_email_agent.py [file]
│  │  ├── test_email_chain.py [file]
│  │  ├── test_fallback_node.py [file]
│  │  ├── test_guardrail_node.py [file]
│  │  ├── test_rag_chain.py [file]
│  │  ├── test_supadantic_integration.py [file]
│  │  └── test_user_input_node.py [file]
│  ├── tools [dir]
│  └── ui [dir]
│     ├── accessibility.md [file]
│     ├── Dockerfile [file]
│     ├── nginx.conf [file]
│     ├── package.json [file]
│     ├── public [dir]
│     ├── README.md [file]
│     ├── src [dir]
│     └── start-dev.sh [file]
├── langgraph-supervisor [dir]
│  ├── langgraph_supervisor [dir]
│  │  ├── __init__.py [file]
│  │  ├── agent_name.py [file]
│  │  ├── handoff.py [file]
│  │  ├── py.typed [file]
│  │  └── supervisor.py [file]
│  ├── LICENSE [file]
│  ├── Makefile [file]
│  ├── pyproject.toml [file]
│  ├── README.md [file]
│  ├── static [dir]
│  │  └── img [dir]
│  ├── tests [dir]
│  │  ├── __init__.py [file]
│  │  ├── test_agent_name.py [file]
│  │  ├── test_supervisor_functional_api.py [file]
│  │  └── test_supervisor.py [file]
│  └── uv.lock [file]
├── langmem [dir]
│  ├── docs [dir]
│  │  ├── _scripts [dir]
│  │  ├── docs [dir]
│  │  ├── mkdocs.yml [file]
│  │  ├── overrides [dir]
│  │  └── README.md [file]
│  ├── examples [dir]
│  │  ├── intro_videos [dir]
│  │  └── standalone_examples [dir]
│  ├── langgraph.json [file]
│  ├── LICENSE [file]
│  ├── Makefile [file]
│  ├── pyproject.toml [file]
│  ├── pytest.ini [file]
│  ├── README.md [file]
│  ├── src [dir]
│  │  └── langmem [dir]
│  ├── tests [dir]
│  │  ├── cassettes [dir]
│  │  ├── conftest.py [file]
│  │  ├── short_term [dir]
│  │  └── test_docstring_examples.py [file]
│  └── uv.lock [file]
├── LLm [dir]
│  └── lmstudio.md [file]
├── llm [dir]
│  ├── docs [dir]
│  │  └── lmstudio.md [file]
│  └── integration [dir]
│     ├── attachments [dir]
│     ├── config.json [file]
│     ├── docker-compose.yml [file]
│     ├── Dockerfile [file]
│     ├── email_analyzer.py [file]
│     ├── email_processor.py [file]
│     ├── models [dir]
│     ├── README.md [file]
│     ├── requirements.txt [file]
│     ├── response_generator.py [file]
│     └── setup.sh [file]
├── llm-integration [dir]
│  ├── attachments [dir]
│  ├── config.json [file]
│  ├── docker-compose.yml [file]
│  ├── Dockerfile [file]
│  ├── email_analyzer.py [file]
│  ├── email_processor.py [file]
│  ├── models [dir]
│  ├── README.md [file]
│  ├── requirements.txt [file]
│  ├── response_generator.py [file]
│  └── setup.sh [file]
├── logo.png [file]
├── logo.webp [file]
├── mcp [dir]
│  ├── brave-search [dir]
│  │  └── start_brave_mcp.sh [file]
│  ├── calendar-semantic-analysis [dir]
│  │  ├── Dockerfile [file]
│  │  ├── mcp-config.json [file]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── setup.py [file]
│  │  ├── src [dir]
│  │  └── test_mcp.py [file]
│  ├── crawler [dir]
│  │  ├── docker-compose.yml [file]
│  │  ├── integracja_crawler_mcp.md [file]
│  │  ├── mcp-crawl4ai-rag [dir]
│  │  ├── mcp-supabase-tools [dir]
│  │  ├── mcp-unified-tools [dir]
│  │  ├── README.md [file]
│  │  ├── test-mcp-services.sh [file]
│  │  └── toolshowto.md [file]
│  ├── db-tools [dir]
│  ├── desktop-commander [dir]
│  ├── dynamic-chatbot-agent-mcp [dir]
│  │  ├── Component_A__Data_Collection_&_Management.vf [file]
│  │  ├── Component_B__Dynamic_Deployed_Chatbot.vf [file]
│  │  ├── Dynamic Chatbot Documentation.pdf [file]
│  │  └── README.md [file]
│  ├── file-scope [dir]
│  │  ├── config.json [file]
│  │  ├── FileScopeMCP-diagram.html [file]
│  │  ├── FileScopeMCP-diagram.png [file]
│  │  ├── FileScopeMCP-excludes.json [file]
│  │  ├── FileScopeMCP-tree.json [file]
│  │  ├── LICENSE [file]
│  │  ├── mcp.json [file]
│  │  ├── mcp.json.linux [file]
│  │  ├── mcp.json.win.txt [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── run.sh [file]
│  │  ├── src [dir]
│  │  └── tsconfig.json [file]
│  ├── foundational-rag [dir]
│  ├── foundational-rag-agent [dir]
│  │  ├── agent [dir]
│  │  ├── database [dir]
│  │  ├── document_processing [dir]
│  │  ├── PLANNING.md [file]
│  │  ├── prompt.txt [file]
│  │  ├── rag-example.sql [file]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── streamlit_ui_example.py [file]
│  │  ├── TASK.md [file]
│  │  ├── tests [dir]
│  │  └── ui [dir]
│  ├── foundational-rag-agent-mcp [dir]
│  │  ├── agent [dir]
│  │  ├── api [dir]
│  │  ├── create_exec_sql_function.sql [file]
│  │  ├── database [dir]
│  │  ├── docker-compose.yml [file]
│  │  ├── Dockerfile [file]
│  │  ├── document_processing [dir]
│  │  ├── PLANNING.md [file]
│  │  ├── prompt.txt [file]
│  │  ├── rag-example-part1.sql [file]
│  │  ├── rag-example-part2.sql [file]
│  │  ├── rag-example-part3.sql [file]
│  │  ├── rag-example-part4.sql [file]
│  │  ├── rag-example.sql [file]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  ├── setup_database.py [file]
│  │  ├── streamlit_ui_example.py [file]
│  │  ├── TASK.md [file]
│  │  ├── test_bielik_connection.py [file]
│  │  ├── tests [dir]
│  │  └── ui [dir]
│  ├── git-mcp [dir]
│  │  ├── app [dir]
│  │  ├── biome.json [file]
│  │  ├── components.json [file]
│  │  ├── img [dir]
│  │  ├── LICENSE [file]
│  │  ├── package.json [file]
│  │  ├── playwright.config.ts [file]
│  │  ├── postcss.config.mjs [file]
│  │  ├── public [dir]
│  │  ├── README.md [file]
│  │  ├── SECURITY.md [file]
│  │  ├── src [dir]
│  │  ├── static [dir]
│  │  ├── tailwind.config.js [file]
│  │  ├── tests [dir]
│  │  ├── tsconfig.cloudflare.json [file]
│  │  ├── tsconfig.json [file]
│  │  ├── tsconfig.node.json [file]
│  │  ├── vite.config.ts [file]
│  │  ├── vitest.config.ts [file]
│  │  ├── worker-configuration.d.ts [file]
│  │  └── wrangler.jsonc [file]
│  ├── light-rag-agent-mcp [dir]
│  │  ├── insert_pydantic_docs.py [file]
│  │  ├── rag_agent.py [file]
│  │  ├── requirements.txt [file]
│  │  ├── streamlit_app.py [file]
│  │  └── super-basic-lightrag.py [file]
│  ├── memory-bank [dir]
│  │  ├── api [dir]
│  │  ├── pipedream-components [dir]
│  │  ├── README.md [file]
│  │  ├── requirements.txt [file]
│  │  └── src [dir]
│  ├── memory-bank-integration [dir]
│  │  ├── api.js [file]
│  │  ├── memory-bank-mcp-client.js [file]
│  │  └── package.json [file]
│  ├── Ottomator [dir]
│  │  └── ottomator-agents [dir]
│  └── tools [dir]
│     ├── check-mcp-server.js [file]
│     ├── mcp-remote-server.js [file]
│     ├── mcp-server-3223.js [file]
│     ├── mcp-server.js [file]
│     ├── sequential-thinking-server.js [file]
│     ├── simple-jsonrpc-server.js [file]
│     ├── simple-mcp-server.js [file]
│     ├── start-mcp-server.js [file]
│     ├── start-vscode-mcp-server.js [file]
│     ├── test-mcp-connection.js [file]
│     └── test-vscode-mcp-server.js [file]
├── mcp-config-standard.json [file]
├── mcp-config-standardized.json [file]
├── mcp-config.json [file]
├── mcp-tools [dir]
│  ├── __init__.py [file]
│  ├── hvac-db-mcp [dir]
│  └── mcp_client.py [file]
├── mcpServers-config.json [file]
├── Mellow [dir]
│  ├── 01_Agent_Statement_LLM_Node.md [file]
│  ├── 01_Introduction_Vision.md [file]
│  ├── 02_Agent_Statement_Tool_Node.md [file]
│  ├── 02_Architectural_Unification.md [file]
│  ├── 03_Agent_Statement_Control_Node.md [file]
│  ├── 03_User_Experience_Unification.md [file]
│  ├── 04_Agent_Statement_Memory_Node.md [file]
│  ├── 04_Data_Intelligence_Unification.md [file]
│  ├── 05_Agent_Statement_Guardrail_Node.md [file]
│  ├── 05_Implementation_Roadmap.md [file]
│  ├── 06_Agent_Statement_Fallback_Node.md [file]
│  ├── 07_Agent_Statement_User_Input_Node.md [file]
│  ├── 08_Agent_Statement_Integration.md [file]
│  ├── 09_Agent_Statement_GSAP_Visualization.md [file]
│  ├── 10_Agent_Statement_Daytona_Integration.md [file]
│  ├── 11_Agent_Statement_RAG_Capabilities.md [file]
│  ├── 12_Agent_Statement_ISO_Compliance.md [file]
│  ├── 7step agent guideline .txt [file]
│  ├── AnimeJS_Inspiration [dir]
│  │  ├── 01_Fluid_Transitions_For_HVAC_UI.md [file]
│  │  ├── 02_Timeline_Orchestration_For_Complex_Workflows.md [file]
│  │  ├── 03_Staggered_Animations_For_Data_Visualization.md [file]
│  │  ├── 04_Morphing_Interfaces_For_Contextual_Workflows.md [file]
│  │  ├── 05_Scroll_Synchronized_Animations.md [file]
│  │  ├── 06_Spring_Physics_For_Natural_Interactions.md [file]
│  │  ├── 07_Hardware_Accelerated_Animations.md [file]
│  │  ├── 08_Reactive_Animations_For_Real_Time_Data.md [file]
│  │  ├── 09_Atomic_Animation_Components.md [file]
│  │  └── 10_Adaptive_Animation_Systems.md [file]
│  ├── Bielik_HVAC_CRM_Orchestration.md [file]
│  ├── docs [dir]
│  │  ├── calendar [dir]
│  │  ├── database [dir]
│  │  ├── device_catalog_integration.md [file]
│  │  ├── executive_dashboard_implementation.md [file]
│  │  ├── executive_dashboard_plan.md [file]
│  │  ├── general [dir]
│  │  ├── gitmcp_daytona_integration.md [file]
│  │  ├── hvac_api_architecture.md [file]
│  │  ├── implementation_progress.md [file]
│  │  ├── integration_architecture.md [file]
│  │  ├── mdl [dir]
│  │  ├── memory-bank [dir]
│  │  ├── README.md [file]
│  │  └── supabase [dir]
│  ├── Dokumentacja [dir]
│  │  └── dynamic_offer_links_implementation.md [file]
│  ├── Futurustic [dir]
│  │  └── DON [dir]
│  ├── HVAC_CRM_Development_Plan.md [file]
│  ├── HVAC_CRM_Master_Plan.md [file]
│  ├── Integration_Bielik_QuantumVisualization.md [file]
│  ├── Opowiadanki.md [file]
│  ├── Plany_na_wczoraj [dir]
│  │  ├── alembic_setup_completed.md [file]
│  │  ├── alembic_setup.md [file]
│  │  ├── analytics_implementation.md [file]
│  │  ├── audio_email_processing.md [file]
│  │  ├── data_access_layer_implementation.md [file]
│  │  ├── data_management.md [file]
│  │  ├── data-collection-integration.md [file]
│  │  ├── do dodania.md [file]
│  │  ├── email_processing_implementation.md [file]
│  │  ├── email_processing_summary.md [file]
│  │  ├── email-integration-success.md [file]
│  │  ├── For_later.md [file]
│  │  ├── gsap-docs.md [file]
│  │  ├── invoice_ocr_processing.md [file]
│  │  ├── iso_compliant_service_completion_implementation.md [file]
│  │  ├── llm_agents.md [file]
│  │  ├── llm_integration_update.md [file]
│  │  ├── market_disruption_strategy.md [file]
│  │  ├── mobile_gsap_plan.md [file]
│  │  ├── musk_vision_implementation.md [file]
│  │  ├── new_architecture_implementation.md [file]
│  │  ├── new_architecture_plan.md [file]
│  │  ├── plan.md [file]
│  │  ├── porady_react_extended.md [file]
│  │  ├── productContext.md [file]
│  │  ├── Profound.md [file]
│  │  ├── progress.md [file]
│  │  ├── projectbrief.md [file]
│  │  ├── qdrant_integration_completed.md [file]
│  │  ├── qdrant_integration.md [file]
│  │  ├── rag_api_endpoints_completed.md [file]
│  │  ├── react_hooks_best_practices.md [file]
│  │  ├── supabase-maintenance.md [file]
│  │  ├── systemPatterns.md [file]
│  │  ├── tech_innovation_roadmap.md [file]
│  │  ├── techContext.md [file]
│  │  ├── TODO [dir]
│  │  └── zakres_funkcjonalny.md [file]
│  ├── raporty przyszlosci [dir]
│  │  ├── bielik-v3-core [dir]
│  │  ├── bielik-v3-core-prototype.md [file]
│  │  ├── Continuus.md [file]
│  │  └── quantum_visualization_production_potential.md [file]
│  └── Real_facts.md [file]
├── memory-bank [dir]
│  ├── activeContext.md [file]
│  ├── alembic-commands.sh [file]
│  ├── alembic.ini [file]
│  ├── analytics_implementation.md [file]
│  ├── api [dir]
│  │  ├── calendar_endpoint.py [file]
│  │  ├── dependencies.py [file]
│  │  └── main.py [file]
│  ├── config.py [file]
│  ├── data_management.md [file]
│  ├── db [dir]
│  │  ├── __init__.py [file]
│  │  ├── migrations [dir]
│  │  ├── models [dir]
│  │  ├── README.md [file]
│  │  ├── repositories [dir]
│  │  └── sqlalchemy_models [dir]
│  ├── do dodania.md [file]
│  ├── email-integration-success.md [file]
│  ├── For_later.md [file]
│  ├── gsap-docs.md [file]
│  ├── langmem_integration.py [file]
│  ├── llm_agents.md [file]
│  ├── market_disruption_strategy.md [file]
│  ├── mobile_gsap_plan.md [file]
│  ├── musk_vision_implementation.md [file]
│  ├── pipedream-components [dir]
│  │  ├── README.md [file]
│  │  └── send-to-api-endpoint.js [file]
│  ├── plan.md [file]
│  ├── porady_react [file]
│  ├── porady_react_extended.md [file]
│  ├── productContext.md [file]
│  ├── Profound.md [file]
│  ├── progress.md [file]
│  ├── projectbrief.md [file]
│  ├── react_hooks_best_practices.md [file]
│  ├── README.md [file]
│  ├── redis_integration.py [file]
│  ├── run-migrations.sh [file]
│  ├── services [dir]
│  │  ├── calendar_event_customer_relation_service.py [file]
│  │  ├── rag [dir]
│  │  └── swan [dir]
│  ├── systemPatterns.md [file]
│  ├── tech_innovation_roadmap.md [file]
│  ├── techContext.md [file]
│  ├── tests [dir]
│  │  ├── __init__.py [file]
│  │  └── test_swan_service.py [file]
│  ├── TO-DO [dir]
│  │  ├── alembic_setup_completed.md [file]
│  │  ├── alembic_setup.md [file]
│  │  ├── audio_email_processing.md [file]
│  │  ├── data_access_layer_implementation.md [file]
│  │  ├── data-collection-integration.md [file]
│  │  ├── email_processing_implementation.md [file]
│  │  ├── email_processing_summary.md [file]
│  │  ├── invoice_ocr_processing.md [file]
│  │  ├── llm_integration_update.md [file]
│  │  ├── new_architecture_implementation.md [file]
│  │  ├── new_architecture_plan.md [file]
│  │  ├── qdrant_integration_completed.md [file]
│  │  ├── qdrant_integration.md [file]
│  │  ├── rag_api_endpoints_completed.md [file]
│  │  └── supabase-maintenance.md [file]
│  ├── utils [dir]
│  │  ├── __init__.py [file]
│  │  ├── config.py [file]
│  │  ├── logging.py [file]
│  │  ├── supabase_client.py [file]
│  │  └── supabase.py [file]
│  └── zakres_funkcjonalny.md [file]
├── memory-bank-integration [dir]
│  ├── config.py [file]
│  ├── README.md [file]
│  ├── requirements.txt [file]
│  ├── services [dir]
│  │  ├── calendar_recurrence.py [file]
│  │  ├── calendar_service.py [file]
│  │  └── email_memory.py [file]
│  ├── tests [dir]
│  │  ├── test_calendar_service.py [file]
│  │  └── test_email_memory.py [file]
│  └── utils [dir]
│     ├── __init__.py [file]
│     └── supabase_client.py [file]
├── migrations [dir]
│  ├── 001_create_base_tables.sql [file]
│  ├── 002_create_rbac_tables.sql [file]
│  ├── 003_create_document_management_tables.sql [file]
│  ├── 004_create_memory_bank_tables.sql [file]
│  ├── 005_create_advanced_relationships.sql [file]
│  ├── 006_create_email_memory_bank.sql [file]
│  ├── 007_create_calendar_attachments_integration.sql [file]
│  ├── 008_device_catalog_integration [dir]
│  │  ├── down.sql [file]
│  │  └── up.sql [file]
│  ├── 009_calendar_event_customer_relations.sql [file]
│  ├── 011_add_calendar_semantic_analysis.sql [file]
│  └── backups [dir]
│     ├── ********_084131 [dir]
│     └── ********_084143 [dir]
├── minimal-augment-chat.json [file]
├── mobile-technician-app [dir]
│  └── README.md [file]
├── mock_bielik_services.py [file]
├── monitor_supabase.sh [file]
├── monitor-supabase.sh [file]
├── najwspanialszy-raport.md [file]
├── next [file]
├── nginx [dir]
│  └── nginx.conf [file]
├── nginx-storage.conf [file]
├── OLD_RETRO [dir]
│  ├── HVAC_CRM_Backend [dir]
│  │  └── Amulet- [dir]
│  ├── KombiedoponentyDoUzycia [dir]
│  │  └── GodFullCrm [dir]
│  ├── Komponent [dir]
│  ├── The_KomponentyDoUżycia [dir]
│  │  └── GodFullCrm [dir]
│  └── TheAgent [dir]
│     ├── agent.py [file]
│     ├── api [dir]
│     ├── app.py [file]
│     ├── docker-compose.yml [file]
│     ├── Dockerfile [file]
│     ├── electron-app [dir]
│     ├── example.py [file]
│     ├── file_processor_worker.py [file]
│     ├── jakontozrobil.md [file]
│     ├── mcp_client.py [file]
│     ├── mcp_config.json [file]
│     ├── mcp_example.py [file]
│     ├── memory_bank [dir]
│     ├── README.md [file]
│     ├── requirements.txt [file]
│     ├── test_agent.py [file]
│     ├── tsconfig.json [file]
│     └── ui [dir]
├── package.json [file]
├── plandex [dir]
│  ├── app [dir]
│  │  ├── clear_local.sh [file]
│  │  ├── cli [dir]
│  │  ├── docker-compose.yml [file]
│  │  ├── plans [dir]
│  │  ├── reset_local.sh [file]
│  │  ├── scripts [dir]
│  │  ├── server [dir]
│  │  ├── shared [dir]
│  │  └── start_local.sh [file]
│  ├── docs [dir]
│  │  ├── babel.config.js [file]
│  │  ├── blog [dir]
│  │  ├── docs [dir]
│  │  ├── docusaurus.config.ts [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── sidebars.ts [file]
│  │  ├── src [dir]
│  │  ├── static [dir]
│  │  └── tsconfig.json [file]
│  ├── images [dir]
│  │  ├── plandex-browser-debug-yt.png [file]
│  │  ├── plandex-intro-vimeo.png [file]
│  │  ├── plandex-logo-dark-bg.png [file]
│  │  ├── plandex-logo-dark-v2.png [file]
│  │  ├── plandex-logo-dark.png [file]
│  │  ├── plandex-logo-light-v2.png [file]
│  │  ├── plandex-logo-light.png [file]
│  │  ├── plandex-logo-thumb.png [file]
│  │  ├── plandex-v2-yt.png [file]
│  │  └── plandex-workflow.png [file]
│  ├── LICENSE [file]
│  ├── plans [dir]
│  │  ├── invite-commands.txt [file]
│  │  ├── model-sets-custom-models-crud.txt [file]
│  │  ├── pdx-file.md [file]
│  │  └── race_cond_chatgpt.txt [file]
│  ├── README.md [file]
│  ├── releases [dir]
│  │  ├── cli [dir]
│  │  ├── images [dir]
│  │  └── server [dir]
│  ├── scripts [dir]
│  │  └── merge_from_reflog.sh [file]
│  └── test [dir]
│     ├── _test_apply.sh [file]
│     ├── error-test.html [file]
│     ├── evals [dir]
│     ├── plan_deletion_test.sh [file]
│     ├── pong [dir]
│     ├── project [dir]
│     └── test_prompts [dir]
├── Plany_na_wczoraj [dir]
│  ├── alembic_setup_completed.md [file]
│  ├── alembic_setup.md [file]
│  ├── analytics_implementation.md [file]
│  ├── audio_email_processing.md [file]
│  ├── data_access_layer_implementation.md [file]
│  ├── data_management.md [file]
│  ├── data-collection-integration.md [file]
│  ├── do dodania.md [file]
│  ├── email_processing_implementation.md [file]
│  ├── email_processing_summary.md [file]
│  ├── email-integration-success.md [file]
│  ├── For_later.md [file]
│  ├── gsap-docs.md [file]
│  ├── invoice_ocr_processing.md [file]
│  ├── iso_compliant_service_completion_implementation.md [file]
│  ├── llm_agents.md [file]
│  ├── llm_integration_update.md [file]
│  ├── market_disruption_strategy.md [file]
│  ├── mobile_gsap_plan.md [file]
│  ├── musk_vision_implementation.md [file]
│  ├── new_architecture_implementation.md [file]
│  ├── new_architecture_plan.md [file]
│  ├── plan.md [file]
│  ├── porady_react_extended.md [file]
│  ├── productContext.md [file]
│  ├── Profound.md [file]
│  ├── progress.md [file]
│  ├── projectbrief.md [file]
│  ├── qdrant_integration_completed.md [file]
│  ├── qdrant_integration.md [file]
│  ├── rag_api_endpoints_completed.md [file]
│  ├── react_hooks_best_practices.md [file]
│  ├── supabase-maintenance.md [file]
│  ├── systemPatterns.md [file]
│  ├── tech_innovation_roadmap.md [file]
│  ├── techContext.md [file]
│  ├── TODO [dir]
│  │  ├── problem_solutions_and_optimizations.md [file]
│  │  ├── stage_01_scope.txt [file]
│  │  ├── stage_02_scope.txt [file]
│  │  ├── stage_03_scope.txt [file]
│  │  ├── stage_04_scope.txt [file]
│  │  ├── stage_05_scope.txt [file]
│  │  ├── stage_06_scope.txt [file]
│  │  ├── stage_07_scope.txt [file]
│  │  ├── stage_08_scope.txt [file]
│  │  ├── stage_09_scope.txt [file]
│  │  ├── stage_10_scope.txt [file]
│  │  ├── stage_11_scope.txt [file]
│  │  ├── stage_12_scope.txt [file]
│  │  ├── status_operacji.md [file]
│  │  ├── system_analysis_and_reflections.md [file]
│  │  ├── technical_debt_and_implementation_priorities.md [file]
│  │  └── workspace_achievements_and_next_steps.md [file]
│  └── zakres_funkcjonalny.md [file]
├── PRODUCTION.md [file]
├── quantum-biosync [dir]
├── quantum-visualization [dir]
│  ├── 2505.05145v1.pdf [file]
│  ├── enhanced [dir]
│  │  ├── index.js [file]
│  │  ├── integration-example.jsx [file]
│  │  ├── quantum-trinity-visualization.css [file]
│  │  ├── QuantumTrinityDemo.jsx [file]
│  │  ├── QuantumTrinityVisualization.jsx [file]
│  │  └── README.md [file]
│  ├── In-Context Learning finally Explained - Quantum AI [English (auto-generated)] [GetSubs.cc].txt [file]
│  ├── integration-example.jsx [file]
│  ├── MEQ Visualization Design.md [file]
│  ├── meq-visualization-no-modules [dir]
│  │  └── meq-visualization [dir]
│  ├── quantum-visualization.css [file]
│  ├── QuantumDemo.jsx [file]
│  ├── quantumUtils.js [file]
│  ├── QuantumVisualization.jsx [file]
│  ├── README.md [file]
│  └── todo.md [file]
├── raport-analizy.md [file]
├── raporty przyszlosci [dir]
│  ├── bielik-v3-core [dir]
│  │  ├── __init__.py [file]
│  │  └── requirements.txt [file]
│  ├── bielik-v3-core-prototype.md [file]
│  ├── Continuus.md [file]
│  └── quantum_visualization_production_potential.md [file]
├── README.md [file]
├── requirements-predictive.txt [file]
├── reset-supabase.sh [file]
├── restore-supabase.sh [file]
├── root [dir]
│  └── hvac-crm [dir]
│     ├── backup-supabase.sh [file]
│     ├── docker-compose.supabase.yml [file]
│     ├── hvac-api [dir]
│     ├── hvac-ui [dir]
│     ├── init-supabase.sh [file]
│     ├── langchain-automation [dir]
│     └── volumes [dir]
├── rozszerzony-raport-analizy.md [file]
├── run_bielik_augment.sh [file]
├── run_calendar_semantic_analysis.py [file]
├── run_email_semantic_tests.sh [file]
├── run_sql_script.js [file]
├── saas-boilerplate@1.7.6 [file]
├── scripts [dir]
│  ├── apply-docker-daemon-config.sh [file]
│  ├── apply-improved-nginx-config.sh [file]
│  ├── calendar_event_customer_relations.py [file]
│  ├── clean-repo.sh [file]
│  ├── database [dir]
│  │  ├── backup.sh [file]
│  │  ├── count_all_records.sh [file]
│  │  ├── restore-data-only.sh [file]
│  │  ├── restore-data.sh [file]
│  │  └── run-migrations.sh [file]
│  ├── deployment [dir]
│  │  ├── deploy-production.sh [file]
│  │  ├── deploy-ui-production.sh [file]
│  │  ├── deploy.sh [file]
│  │  └── install-service.sh [file]
│  ├── docker-daemon-config.json [file]
│  ├── integrate_gitmcp_daytona.py [file]
│  ├── maintenance [dir]
│  │  ├── autostart.sh [file]
│  │  ├── cleanup-old-directories.sh [file]
│  │  ├── setup-cron.sh [file]
│  │  ├── update-config.sh [file]
│  │  └── validate-config.sh [file]
│  ├── master-docker-optimization.sh [file]
│  ├── mcp [dir]
│  │  ├── restart-vscode.sh [file]
│  │  ├── start-bielik-mcp-standardized.sh [file]
│  │  ├── start-mcp-server-3223.sh [file]
│  │  ├── start-mcp-server.sh [file]
│  │  ├── start-vscode-mcp-server-standardized.sh [file]
│  │  ├── start-vscode-mcp-server.sh [file]
│  │  ├── test-mcp-tools.sh [file]
│  │  └── toggle-vscode-mcp-server.sh [file]
│  ├── monitor-database.sh [file]
│  ├── monitor-docker-memory.sh [file]
│  ├── monitor-supabase-performance.py [file]
│  ├── monitoring [dir]
│  │  └── monitor.sh [file]
│  ├── optimize-database.sh [file]
│  ├── optimize-docker-containers.sh [file]
│  ├── README_calendar_event_customer_relations.md [file]
│  ├── restart-unhealthy-containers.sh [file]
│  ├── run_calendar_event_customer_relations.py [file]
│  ├── run_gitmcp.sh [file]
│  ├── run-migrations.sh [file]
│  ├── setup_gitmcp_daytona.sh [file]
│  ├── setup-docker-cron-jobs.sh [file]
│  ├── storage [dir]
│  │  ├── fix-storage-final-v2.sh [file]
│  │  ├── fix-storage-final-v3.sh [file]
│  │  ├── fix-storage-final.sh [file]
│  │  ├── fix-supabase-storage-complete.sh [file]
│  │  ├── fix-supabase-storage.sh [file]
│  │  ├── init-storage-no-migrations.sh [file]
│  │  ├── restart-storage-v2.sh [file]
│  │  ├── restart-storage.sh [file]
│  │  └── run-nginx-storage.sh [file]
│  ├── supabase [dir]
│  │  ├── check-supabase.sh [file]
│  │  ├── clean-supabase.sh [file]
│  │  ├── start-supabase.sh [file]
│  │  └── stop-supabase.sh [file]
│  ├── test_gitmcp.py [file]
│  ├── test_mcp_gitmcp.py [file]
│  └── update-supabase-imports.js [file]
├── services [dir]
│  ├── api [dir]
│  │  ├── calendar_analysis_api.py [file]
│  │  ├── docker-compose.calendar-analysis.yml [file]
│  │  ├── Dockerfile.calendar-analysis [file]
│  │  └── requirements.txt [file]
│  ├── calendar [dir]
│  │  ├── calendar_db_updater.py [file]
│  │  ├── calendar_recurrence.py [file]
│  │  ├── calendar_semantic_analyzer.py [file]
│  │  ├── calendar_semantic_integration.py [file]
│  │  ├── calendar_service.py [file]
│  │  └── calendar_sharing.py [file]
│  ├── email [dir]
│  │  ├── email_memory.py [file]
│  │  ├── email_processor.py [file]
│  │  └── email_service.py [file]
│  ├── email_service.py [file]
│  ├── nginx [dir]
│  │  └── html [dir]
│  └── scheduler [dir]
│     └── calendar_analysis_task.py [file]
├── setup-cron.sh [file]
├── setup-supabase.sh [file]
├── ShadCN-context.md [file]
├── shared [dir]
│  └── supabase [dir]
│     ├── cache.py [file]
│     ├── client.py [file]
│     ├── client.ts [file]
│     ├── connection_pool.py [file]
│     ├── hooks [dir]
│     ├── index.ts [file]
│     ├── profiler.py [file]
│     └── README.md [file]
├── simple_bielik_test.py [file]
├── simple-mcpServers.json [file]
├── start_bielik_mcp_fixed.sh [file]
├── start_bielik_mcp.sh [file]
├── start-supabase-full.sh [file]
├── start-supabase-simple.sh [file]
├── start-supabase.sh [file]
├── stop_bielik_mcp.sh [file]
├── stop-supabase.sh [file]
├── supabase [dir]
│  └── config.toml [file]
├── SUPABASE_DOCKER_GUIDE.md [file]
├── SUPABASE_ENHANCED_SETUP.md [file]
├── Supabase_git [dir]
│  ├── cli [dir]
│  │  ├── api [dir]
│  │  ├── cmd [dir]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── docs [dir]
│  │  ├── examples [dir]
│  │  ├── go.mod [file]
│  │  ├── go.sum [file]
│  │  ├── internal [dir]
│  │  ├── LICENSE [file]
│  │  ├── main.go [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── scripts [dir]
│  │  └── tools [dir]
│  ├── langchain-supabase-website-chatbot [dir]
│  │  ├── components [dir]
│  │  ├── config [dir]
│  │  ├── next.config.js [file]
│  │  ├── package.json [file]
│  │  ├── pages [dir]
│  │  ├── postcss.config.cjs [file]
│  │  ├── public [dir]
│  │  ├── README.md [file]
│  │  ├── schema.sql [file]
│  │  ├── scripts [dir]
│  │  ├── styles [dir]
│  │  ├── tailwind.config.cjs [file]
│  │  ├── tsconfig.json [file]
│  │  ├── types [dir]
│  │  ├── utils [dir]
│  │  └── visual-guide [dir]
│  ├── supabase [dir]
│  │  ├── apps [dir]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── DEVELOPERS.md [file]
│  │  ├── docker [dir]
│  │  ├── examples [dir]
│  │  ├── i18n [dir]
│  │  ├── knip.jsonc [file]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── package.json [file]
│  │  ├── packages [dir]
│  │  ├── pnpm-workspace.yaml [file]
│  │  ├── README.md [file]
│  │  ├── scripts [dir]
│  │  ├── SECURITY.md [file]
│  │  ├── supa-mdx-lint [dir]
│  │  ├── supa-mdx-lint.config.toml [file]
│  │  ├── supabase [dir]
│  │  ├── tests [dir]
│  │  ├── tsconfig.json [file]
│  │  └── turbo.json [file]
│  ├── supabase-grafana [dir]
│  │  ├── dashboard.json [file]
│  │  ├── docker-compose.yml [file]
│  │  ├── Dockerfile [file]
│  │  ├── docs [dir]
│  │  ├── entrypoint.sh [file]
│  │  ├── fly.toml [file]
│  │  ├── grafana [dir]
│  │  ├── LICENSE [file]
│  │  ├── Makefile [file]
│  │  ├── prometheus [dir]
│  │  ├── README.md [file]
│  │  └── supervisord.conf [file]
│  ├── supabase-js [dir]
│  │  ├── docs [dir]
│  │  ├── jest.config.ts [file]
│  │  ├── jsr.json [file]
│  │  ├── LICENSE [file]
│  │  ├── package.json [file]
│  │  ├── README.md [file]
│  │  ├── RELEASE.md [file]
│  │  ├── src [dir]
│  │  ├── test [dir]
│  │  ├── tsconfig.json [file]
│  │  ├── tsconfig.module.json [file]
│  │  └── webpack.config.js [file]
│  ├── supabase-mcp [dir]
│  │  ├── docs [dir]
│  │  ├── LICENSE [file]
│  │  ├── package.json [file]
│  │  ├── packages [dir]
│  │  ├── README.md [file]
│  │  └── supabase [dir]
│  ├── supabase-mcp-server [dir]
│  │  ├── CHANGELOG.MD [file]
│  │  ├── codecov.yml [file]
│  │  ├── CONTRIBUTING.MD [file]
│  │  ├── Dockerfile [file]
│  │  ├── LICENSE [file]
│  │  ├── llms-full.txt [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── smithery.yaml [file]
│  │  ├── supabase_mcp [dir]
│  │  ├── tests [dir]
│  │  └── uv.lock [file]
│  ├── supabase-py [dir]
│  │  ├── CHANGELOG.md [file]
│  │  ├── CODE_OF_CONDUCT.md [file]
│  │  ├── CONTRIBUTING.md [file]
│  │  ├── docs [dir]
│  │  ├── LICENSE [file]
│  │  ├── MAINTAINERS.md [file]
│  │  ├── Makefile [file]
│  │  ├── poetry_scripts.py [file]
│  │  ├── pyproject.toml [file]
│  │  ├── README.md [file]
│  │  ├── release-please-config.json [file]
│  │  ├── supabase [dir]
│  │  ├── test.ps1 [file]
│  │  └── tests [dir]
│  └── supabase-schema [dir]
│     ├── api [dir]
│     ├── components.d.ts [file]
│     ├── images [dir]
│     ├── index.html [file]
│     ├── LICENSE.txt [file]
│     ├── package.json [file]
│     ├── public [dir]
│     ├── README.md [file]
│     ├── src [dir]
│     ├── tsconfig.json [file]
│     ├── vercel.json [file]
│     ├── vite.config.ts [file]
│     └── windi.config.js [file]
├── SUPABASE_README.md [file]
├── SUPABASE_SETUP_GUIDE.md [file]
├── SUPABASE_SETUP.md [file]
├── SUPABASE_STABILITY_GUIDE.md [file]
├── SUPABASE_UPDATE_GUIDE.md [file]
├── SUPABASE_UPGRADE.md [file]
├── supabase-manager.sh [file]
├── supabase-manager.updated.sh [file]
├── supabase-reset-and-fix.sh [file]
├── SUPABASE-SETUP.md [file]
├── test_augment_bielik.py [file]
├── test_bielik_email_analysis.py [file]
├── test_bielik_mcp_api.py [file]
├── test_bielik_mcp_client_fixed.py [file]
├── test_bielik_mcp_client.py [file]
├── test_bielik_mcp_fix.py [file]
├── test_bielik_mcp_httpx.py [file]
├── test_bielik_mcp_simple.py [file]
├── test_bielik_mcp_success.py [file]
├── test_bielik_mcp.py [file]
├── test_bielik_mcp.sh [file]
├── test_directory [dir]
├── test_email_memory_integration.py [file]
├── test_email_semantic_processing.py [file]
├── test_fastmcp_client.py [file]
├── test_mcp_client.py [file]
├── test_mcp_use_bielik.py [file]
├── test_process_text.json [file]
├── test_supabase_connection.py [file]
├── test-api-endpoint.js [file]
├── test-bielik-mcp-tools.js [file]
├── test-bielik-mcp.js [file]
├── test-bielik-mcp.sh [file]
├── test-calendar-api.js [file]
├── test-pipedream-component.js [file]
├── test-supabase-connection.js [file]
├── tests [dir]
│  └── test_calendar_semantic_analyzer.py [file]
├── tools [dir]
│  ├── mcp [dir]
│  │  ├── check-mcp-server.js [file]
│  │  ├── mcp-remote-server.js [file]
│  │  ├── mcp-server-3223.js [file]
│  │  ├── mcp-server.js [file]
│  │  ├── sequential-thinking-server.js [file]
│  │  ├── simple-jsonrpc-server.js [file]
│  │  ├── simple-mcp-server.js [file]
│  │  ├── start-mcp-server.js [file]
│  │  ├── start-vscode-mcp-server.js [file]
│  │  ├── test-mcp-connection.js [file]
│  │  └── test-vscode-mcp-server.js [file]
│  ├── pomysły.md [file]
│  └── qdrant [dir]
│     ├── basic_qdrant_test.py [file]
│     ├── check_qdrant_http.py [file]
│     ├── check_qdrant_info.py [file]
│     ├── create_qdrant_collection.py [file]
│     └── test_qdrant_connection.py [file]
├── trinity-interface-prototype [dir]
│  ├── IMPLEMENTATION.md [file]
│  ├── index.html [file]
│  ├── README.md [file]
│  ├── script.js [file]
│  ├── server.js [file]
│  ├── start.sh [file]
│  └── styles.css [file]
├── troubleshoot-supabase.sh [file]
├── uno.md [file]
├── updated-pipedream-component.js [file]
├── VariousMCP [dir]
│  └── FileScopeMCP [dir]
│     ├── config.json [file]
│     ├── FileScopeMCP-diagram.html [file]
│     ├── FileScopeMCP-diagram.png [file]
│     ├── FileScopeMCP-excludes.json [file]
│     ├── FileScopeMCP-tree.json [file]
│     ├── LICENSE [file]
│     ├── mcp.json [file]
│     ├── mcp.json.linux [file]
│     ├── mcp.json.win.txt [file]
│     ├── package.json [file]
│     ├── README.md [file]
│     ├── run.sh [file]
│     ├── src [dir]
│     └── tsconfig.json [file]
├── volumes [dir]
│  ├── api [dir]
│  │  ├── kong-custom.yml [file]
│  │  ├── kong.yml [file]
│  │  └── kong.yml.backup [file]
│  ├── db [dir]
│  │  ├── _supabase.sql [file]
│  │  ├── data [dir]
│  │  ├── init [dir]
│  │  ├── init-scripts [dir]
│  │  ├── jwt.sql [file]
│  │  ├── pooler.sql [file]
│  │  ├── realtime.sql [file]
│  │  ├── roles.sql [file]
│  │  └── webhooks.sql [file]
│  ├── kong-runtime-config [dir]
│  └── storage [dir]
├── workflow-automation [dir]
│  └── README.md [file]
└── Zrzut ekranu 2025-05-10 061011.png [file]