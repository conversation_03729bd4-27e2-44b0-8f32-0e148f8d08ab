name: Run Unit Test For SDKs

on:
  pull_request:
    branches:
      - main
    paths:
      - sdks/**

concurrency:
  group: sdk-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build:
    name: unit test for Node.js SDK
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16, 18, 20, 22]

    defaults:
      run:
        working-directory: sdks/nodejs-client

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: ''
          cache-dependency-path: 'pnpm-lock.yaml'

      - name: Install Dependencies
        run: pnpm install --frozen-lockfile

      - name: Test
        run: pnpm test
