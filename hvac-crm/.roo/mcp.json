{"mcpServers": {"bielik-mcp": {"transport": "http", "url": "http://localhost:8052/mcp", "alwaysAllow": ["process_text", "analyze_vat_invoice", "generate_vat_report", "client_chat", "trinity_process"], "disabled": true}, "desktop-commander": {"command": "npx", "args": ["@wonderwhy-er/desktop-commander", "-y"], "autoApprove": ["test"], "alwaysAllow": ["get_config", "set_config_value", "read_file", "read_multiple_files", "write_file", "create_directory", "list_directory", "move_file", "search_files", "search_code", "get_file_info", "edit_block", "execute_command", "read_output", "force_terminate", "list_sessions", "list_processes", "kill_process", "write_to_file"]}, "crawl4ai-rag": {"transport": "sse", "url": "http://localhost:8051/sse", "alwaysAllow": ["crawl_single_page", "smart_crawl_url", "get_available_sources", "query_rag", "get_embeddings", "perform_rag_query"], "disabled": true}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "hyperbrowser": {"command": "npx", "args": ["-y", "hyperbrowser-mcp"], "env": {"HYPERBROWSER_API_KEY": "hb_f92026e749c17c16f3eda2fa1b7f"}, "alwaysAllow": ["scrape_webpage", "crawl_webpages", "extract_structured_data", "browser_use_agent", "openai_computer_use_agent", "claude_computer_use_agent", "search_with_bing", "create_profile", "delete_profile", "list_profiles"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "supabase": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://postgres:Blaeritipol1@127.0.0.1:5433/postgres"]}, "servers": {"memory": {"command": "docker", "args": ["run", "-i", "-v", "claude-memory:/app/dist", "--rm", "mcp/memory"]}}}}