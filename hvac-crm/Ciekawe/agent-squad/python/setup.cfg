[metadata]
name = agent_squad
version = 1.0.0
author = <PERSON>, <PERSON><PERSON><PERSON><PERSON>
author_email = brna<PERSON>@amazon.com, <EMAIL>
description = Agent Squad framework
long_description = file: README.md
long_description_content_type = text/markdown
license = Apache License 2.0
license_files = LICENSE
url = https://github.com/awslabs/agent-squad
classifiers =
    Programming Language :: Python :: 3
    License :: OSI Approved :: Apache Software License
    Operating System :: OS Independent

[options]
package_dir =
    = src
packages = find:
python_requires = >=3.11

[options.extras_require]
aws =
    boto3>=1.36.18
anthropic =
    anthropic>=0.40.0
openai =
    openai>=1.55.3
sql =
    libsql-client>=0.3.1
all =
    anthropic>=0.40.0
    openai>=1.55.3
    boto3>=1.36.18
    libsql-client>=0.3.1

[options.packages.find]
where = src
exclude =
    tests*
