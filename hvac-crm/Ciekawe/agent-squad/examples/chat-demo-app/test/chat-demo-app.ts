// import * as cdk from 'aws-cdk-lib';
// import { Template } from 'aws-cdk-lib/assertions';
// import * as ChatDemoStack from '../lib/chat-demo-stack';

// example test. To run these tests, uncomment this file along with the
// example resource in lib/chat-demo-stack.ts
test('SQS Queue Created', () => {
//   const app = new cdk.App();
//     // WHEN
//   const stack = new ChatDemoStack.ChatDemoStack(app, 'ChatDemoStack');
//     // THEN
//   const template = Template.fromStack(stack);

//   template.hasResourceProperties('AWS::SQS::Queue', {
//     VisibilityTimeout: 300
//   });
});
