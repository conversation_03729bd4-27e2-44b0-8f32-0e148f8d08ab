{"name": "chat-demo-app", "version": "0.1.0", "bin": {"chat-demo-app": "bin/chat-demo-app.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "postinstall": "cd lambda/auth && npm install && cd ../.."}, "devDependencies": {"@aws-lambda-powertools/parameters": "^2.3.0", "@types/jest": "^29.5.12", "@types/node": "^20.14.2", "aws-cdk": "2.148.1", "jest": "^29.7.0", "ts-jest": "^29.1.4", "ts-node": "^10.9.2", "typescript": "~5.4.5"}, "dependencies": {"@aws-cdk/aws-cognito-identitypool-alpha": "^2.158.0-alpha.0", "@aws-cdk/aws-lambda-python-alpha": "^2.158.0-alpha.0", "@aws-lambda-powertools/logger": "^2.3.0", "@aws-sdk/client-bedrock-agent": "^3.675.0", "@aws-sdk/client-bedrock-runtime": "^3.651.1", "@aws-sdk/core": "^3.651.1", "@opensearch-project/opensearch": "^2.12.0", "aws-cdk-lib": "^2.187.0", "aws-lambda": "^1.0.7", "constructs": "^10.0.0", "esbuild": "^0.24.0", "i": "^0.3.7", "agent-squad": "^0.0.17", "natural": "^7.1.0", "npm": "^10.8.1", "source-map-support": "^0.5.21", "stopword": "^3.0.1", "ts-retry": "^5.0.1", "xml2js": "^0.6.2"}}