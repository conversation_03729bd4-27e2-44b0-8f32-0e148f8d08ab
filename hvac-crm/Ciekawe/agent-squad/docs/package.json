{"name": "@agent-squad/docs", "description": "The official documentation for Agent Squad", "type": "module", "version": "0.7.0", "private": true, "scripts": {"dev": "npx astro dev", "start": "npx astro dev", "build": "npx astro build", "preview": "npx astro preview", "astro": "npx astro", "audit": "npm audit", "clean": "npx rimraf .astro/ node_modules/ dist/"}, "author": {"name": "Amazon Web Services", "url": "https://aws.amazon.com"}, "repository": {"type": "git", "url": "git://github.com/awslabs/agent-squad"}, "license": "Apache-2.0", "dependencies": {"@astrojs/starlight": "^0.30.3", "astro": "^5.1.1", "sharp": "^0.33.4", "shiki": "^1.10.3"}, "devDependencies": {"rimraf": "^5.0.7"}}