---
title: Classifier overview
description: An introduction to the Classifier in the Agent Squad
---

The Classifier is a crucial component of the Agent Squad, responsible for analyzing user input and identifying the most appropriate agents. The orchestrator supports multiple classifier implementations, with Bedrock Classifier and Anthropic Classifier being the primary options.

## Available Classifiers

- **[Bedrock Classifier](/agent-squad/classifiers/built-in/bedrock-classifier)** leverages Amazon Bedrock's AI models for intent classification. It is the default classifier used by the orchestrator.

- **[Anthropic Classifier](/agent-squad/classifiers/built-in/anthropic-classifier)** uses Anthropic's AI models for intent classification. It provides an alternative option for users who prefer or have access to Anthropic's services.

- **[OpenAI Classifier](/agent-squad/classifiers/built-in/openai-classifier)** uses OpenAI's AI models for intent classification. It provides an alternative option for users who prefer or have access to OpenAI's services.


### Process Flow

Regardless of the classifier used, the general process remains the same:

1. User input is collected by the orchestrator.
2. The Classifier performs input analysis, considering:
   - Conversation history across all agents
   - Individual agent profiles and capabilities
3. The most suitable agent is determined.

By default, if no agent is selected the orchestrator can be configured to:

A. Use a default agent (a **[BedrockLLMAgent for example](/agent-squad/agents/built-in/bedrock-llm-agent)**)

B. Return a message prompting the user for more information.

This behavior can be customized using the `USE_DEFAULT_AGENT_IF_NONE_IDENTIFIED` and `NO_SELECTED_AGENT_MESSAGE` configuration options in the orchestrator.

For a detailed explanation of these options and other orchestrator configurations, please refer to the [Orchestrator Overview](/agent-squad/orchestrator/overview#agent-selection-and-default-behavior) page.

The classifier's decision-making process is crucial for the effective routing of user queries to the most appropriate agent, ensuring a seamless and efficient multi-agent interaction experience.
### Initialization

When you create a new Orchestrator by initializing a `AgentSquad` the default Bedrock Classifier is initialized.

import { Tabs, TabItem } from '@astrojs/starlight/components';

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    const orchestrator = new AgentSquad();
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    from agent_squad.orchestrator import AgentSquad

    orchestrator = AgentSquad()
    ```
  </TabItem>
</Tabs>

To use the Anthropic Classifier, you can pass it as an option:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    import { AnthropicClassifier } from "agent-squad";

    const anthropicClassifier = new AnthropicClassifier({
      apiKey: 'your-anthropic-api-key'
    });
    const orchestrator = new AgentSquad({ classifier: anthropicClassifier });
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    from agent_squad.orchestrator import AgentSquad
    from agent_squad.classifiers import AnthropicClassifier, AnthropicClassifierOptions

    anthropic_classifier = AnthropicClassifier(AnthropicClassifierOptions(
        api_key='your-anthropic-api-key'
    ))
    orchestrator = AgentSquad(classifier=anthropic_classifier)
    ```
  </TabItem>
</Tabs>

## Custom Classifier Implementation

You can provide your own custom implementation of the classifier by extending the abstract `Classifier` class. For details on how to do this, please refer to the [Custom Classifier](/agent-squad/classifiers/custom-classifier) section.

## Testing

You can test any Classifier directly using the `classify` method:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    const response = await orchestrator.classifyIntent(userInput, userId, sessionId);
    console.log('\n** RESPONSE ** \n');
    console.log(` > Agent ID: ${response.selectedAgent?.id}`);
    console.log(` > Agent Name: ${response.selectedAgent?.name}`);
    console.log(` > Confidence: ${response.confidence}\n`);
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    import asyncio

    async def test_classifier():
        user_input = "What are the symptoms of the flu?"
        user_id = "test_user"
        session_id = "test_session"

        # Fetch chat history (this might vary depending on your implementation)
        chat_history = await orchestrator.storage.fetch_all_chats(user_id, session_id)

        # Classify the input
        response = await orchestrator.classifier.classify(user_input, chat_history)

        print('\n** RESPONSE ** \n')
        print(f" > Agent ID: {response.selected_agent.id if response.selected_agent else 'None'}")
        print(f" > Agent Name: {response.selected_agent.name if response.selected_agent else 'None'}")
        print(f" > Confidence: {response.confidence}\n")

    # Run the async function
    asyncio.run(test_classifier())
    ```
  </TabItem>
</Tabs>

This allows you to:
- Verify the Classifier's decision-making process
- Test different inputs and conversation scenarios
- Fine-tune the system prompt or agent descriptions

## Common Issues

- **Misclassification**: If you notice frequent misclassifications, review and update agent descriptions or adjust the system prompt.
- **API Key Issues**: For AnthropicClassifier, ensure your API key is valid and properly configured.
- **Model Availability**: For BedrockClassifier, ensure you have access to the specified Amazon Bedrock model in your AWS account.

## Choosing the Right Classifier

When deciding between different classifiers, consider:

1. **API Access**: Which service you have access to and prefer.
2. **Model Performance**: Test classifiers with your specific use case to determine which performs better for your needs.
3. **Cost**: Compare the pricing structures for your expected usage.

By thoroughly testing and debugging your chosen Classifier, you can ensure accurate intent classification and efficient query routing in your Agent Squad.

## Direct Classifier Access

### With Context Management
Test the classifier with full conversation history handling:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    const response = await orchestrator.classifyRequest(userInput, userId, sessionId);
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    response = await orchestrator.classify_request(user_input, user_id, session_id)
    ```
  </TabItem>
</Tabs>

This method:
- Retrieves conversation history automatically
- Maintains context across test calls
- Ideal for end-to-end testing

### Without Context
Test raw classification without conversation history:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    const response = await orchestrator.classifier.classify(userInput, []);
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    response = await orchestrator.classifier.classify(user_input, [])
    ```
  </TabItem>
</Tabs>

Best for:
- Prompt tuning
- Single-input validation
- Classification unit tests

---

For more detailed information on each classifier, refer to the [BedrockClassifier](/agent-squad/classifiers/built-in/bedrock-classifier) and [AnthropicClassifier](/classifiers/built-in/anthropic-classifier) documentation pages.