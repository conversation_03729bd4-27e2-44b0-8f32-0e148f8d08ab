---
title: Anthropic Classifier
description: How to configure the Anthropic classifier
---

The Anthropic Classifier is an alternative classifier for the Agent Squad that leverages Anthropic's AI models for intent classification. It provides powerful classification capabilities using Anthropic's state-of-the-art language models.

The Anthropic Classifier extends the abstract `Classifier` class and uses the Anthropic API client to process requests and classify user intents.

## Features

- Utilizes Anthropic's AI models (e.g., Claude) for intent classification
- Configurable model selection and inference parameters
- Supports custom system prompts and variables
- <PERSON>les conversation history for context-aware classification

### Default Model

The classifier uses Claude 3.5 Sonnet as its default model:
```typescript
ANTHROPIC_MODEL_ID_CLAUDE_3_5_SONNET = "claude-3-5-sonnet-20240620"
```

### Python Package

If you haven't already installed the Anthropic-related dependencies, make sure to install them:

```bash
pip install "agent-squad[anthropic]"
```

### Basic Usage

To use the AnthropicClassifier, you need to create an instance with your Anthropic API key and pass it to the Agent Squad:

import { Tabs, TabItem } from '@astrojs/starlight/components';

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    import { AnthropicClassifier } from "agent-squad";
    import { AgentSquad } from "agent-squad";

    const anthropicClassifier = new AnthropicClassifier({
      apiKey: 'your-anthropic-api-key'
    });

    const orchestrator = new AgentSquad({ classifier: anthropicClassifier });
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    from agent_squad.classifiers import AnthropicClassifier, AnthropicClassifierOptions
    from agent_squad.orchestrator import AgentSquad

    anthropic_classifier = AnthropicClassifier(AnthropicClassifierOptions(
        api_key='your-anthropic-api-key'
    ))

    orchestrator = AgentSquad(classifier=anthropic_classifier)
    ```
  </TabItem>
</Tabs>

## System Prompt and Variables

### Full Default System Prompt

The default system prompt used by the classifier is comprehensive and includes examples of both simple and complex interactions:

```
You are AgentMatcher, an intelligent assistant designed to analyze user queries and match them with
the most suitable agent or department. Your task is to understand the user's request,
identify key entities and intents, and determine which agent or department would be best equipped
to handle the query.

Important: The user's input may be a follow-up response to a previous interaction.
The conversation history, including the name of the previously selected agent, is provided.
If the user's input appears to be a continuation of the previous conversation
(e.g., "yes", "ok", "I want to know more", "1"), select the same agent as before.

Analyze the user's input and categorize it into one of the following agent types:
<agents>
{{AGENT_DESCRIPTIONS}}
</agents>
If you are unable to select an agent put "unknown"

Guidelines for classification:

    Agent Type: Choose the most appropriate agent type based on the nature of the query.
    For follow-up responses, use the same agent type as the previous interaction.
    Priority: Assign based on urgency and impact.
        High: Issues affecting service, billing problems, or urgent technical issues
        Medium: Non-urgent product inquiries, sales questions
        Low: General information requests, feedback
    Key Entities: Extract important nouns, product names, or specific issues mentioned.
    For follow-up responses, include relevant entities from the previous interaction if applicable.
    For follow-ups, relate the intent to the ongoing conversation.
    Confidence: Indicate how confident you are in the classification.
        High: Clear, straightforward requests or clear follow-ups
        Medium: Requests with some ambiguity but likely classification
        Low: Vague or multi-faceted requests that could fit multiple categories
    Is Followup: Indicate whether the input is a follow-up to a previous interaction.

Handle variations in user input, including different phrasings, synonyms,
and potential spelling errors.
For short responses like "yes", "ok", "I want to know more", or numerical answers,
treat them as follow-ups and maintain the previous agent selection.

Here is the conversation history that you need to take into account before answering:
<history>
{{HISTORY}}
</history>

Skip any preamble and provide only the response in the specified format.
```

### Variable Replacements

#### AGENT_DESCRIPTIONS Example
```
tech-support-agent:Specializes in resolving technical issues, software problems, and system configurations
billing-agent:Handles all billing-related queries, payment processing, and subscription management
customer-service-agent:Manages general inquiries, account questions, and product information requests
sales-agent:Assists with product recommendations, pricing inquiries, and purchase decisions
```

### Extended HISTORY Examples

The conversation history is formatted to include agent names in the responses, allowing the classifier to track which agent handled each interaction. Each assistant response is prefixed with `[agent-name]` in the history, making it clear who provided each response:

```
user: I need help with my subscription
assistant: [billing-agent] I can help you with your subscription. What specific information do you need?
user: The premium features aren't working
assistant: [tech-support-agent] I'll help you troubleshoot the premium features. Could you tell me which specific features aren't working?
user: The cloud storage says I only have 5GB but I'm supposed to have 100GB
assistant: [tech-support-agent] Let's verify your subscription status and refresh your storage allocation. When did you last see the correct storage amount?
user: How much am I paying for this subscription?
assistant: [billing-agent] I'll check your subscription details. Your current plan is $29.99/month for the Premium tier with 100GB storage. Would you like me to review your billing history?
user: Yes please
```

Here, the history shows the conversation moving between `billing-agent` and `tech-support-agent` as the topic shifts between billing and technical issues.


The agent prefixing (e.g., `[agent-name]`) is automatically handled by the Agent Squad when formatting the conversation history. This helps the classifier understand:
- Which agent handled each part of the conversation
- The context of previous interactions
- When agent transitions occurred
- How to maintain continuity for follow-up responses

## Tool-Based Response Structure

The AnthropicClassifier uses a tool specification to enforce structured output from the model. This is a design pattern that ensures consistent and properly formatted responses.

### The Tool Specification
```json
{
  "name": "analyzePrompt",
  "description": "Analyze the user input and provide structured output",
  "input_schema": {
    "type": "object",
    "properties": {
      "userinput": {"type": "string"},
      "selected_agent": {"type": "string"},
      "confidence": {"type": "number"}
    },
    "required": ["userinput", "selected_agent", "confidence"]
  }
}
```

### Why Use Tools?

1. **Structured Output**: Instead of free-form text, the model must provide exactly the data structure we need.
2. **Guaranteed Format**: The tool schema ensures we always get:
   - A valid agent identifier
   - A properly formatted confidence score
   - All required fields
3. **Implementation Note**: The tool isn't actually executed - it's a pattern to force the model to structure its response in a specific way that maps directly to our `ClassifierResult` type.

Example Response:
```json
{
  "userinput": "I need to reset my password",
  "selected_agent": "tech-support-agent",
  "confidence": 0.95
}
```

### Customizing the System Prompt

You can override the default system prompt while maintaining the required agent descriptions and history variables. Here's how to do it:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    orchestrator.classifier.setSystemPrompt(
      `You are a specialized routing expert with deep knowledge of {{INDUSTRY}} operations.

      Your available agents are:
      <agents>
      {{AGENT_DESCRIPTIONS}}
      </agents>

      Consider these key factors for {{INDUSTRY}} when routing:
      {{INDUSTRY_RULES}}

      Recent conversation context:
      <history>
      {{HISTORY}}
      </history>

      Route based on industry best practices and conversation history.`,
      {
        INDUSTRY: "healthcare",
        INDUSTRY_RULES: [
          "- HIPAA compliance requirements",
          "- Patient data privacy protocols",
          "- Emergency request prioritization",
          "- Insurance verification processes"
        ]
      }
    );
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    orchestrator.classifier.set_system_prompt(
        """You are a specialized routing expert with deep knowledge of {{INDUSTRY}} operations.

        Your available agents are:
        <agents>
        {{AGENT_DESCRIPTIONS}}
        </agents>

        Consider these key factors for {{INDUSTRY}} when routing:
        {{INDUSTRY_RULES}}

        Recent conversation context:
        <history>
        {{HISTORY}}
        </history>

        Route based on industry best practices and conversation history.""",
        {
            "INDUSTRY": "healthcare",
            "INDUSTRY_RULES": [
                "- HIPAA compliance requirements",
                "- Patient data privacy protocols",
                "- Emergency request prioritization",
                "- Insurance verification processes"
            ]
        }
    )
    ```
  </TabItem>
</Tabs>

Note: When customizing the prompt, you must include:
- The `{{AGENT_DESCRIPTIONS}}` variable to list available agents
- The `{{HISTORY}}` variable for conversation context
- Clear instructions for agent selection
- Response format expectations

## Configuration Options

The AnthropicClassifier accepts the following configuration options:

- `api_key` (required): Your Anthropic API key.
- `model_id` (optional): The ID of the Anthropic model to use. Defaults to Claude 3.5 Sonnet.
- `inference_config` (optional): A dictionary containing inference configuration parameters:
  - `max_tokens` (optional): The maximum number of tokens to generate. Defaults to 1000.
  - `temperature` (optional): Controls randomness in output generation.
  - `top_p` (optional): Controls diversity of output generation.
  - `stop_sequences` (optional): A list of sequences that will stop generation.

## Best Practices

1. **API Key Security**: Keep your Anthropic API key secure and never expose it in your code.
2. **Model Selection**: Choose appropriate models based on your needs and performance requirements.
3. **Inference Configuration**: Experiment with different parameters to optimize classification accuracy.
4. **System Prompt**: Consider customizing the system prompt for your specific use case, while maintaining the core classification structure.

## Limitations

- Requires an active Anthropic API key
- Subject to Anthropic's API pricing and rate limits
- Classification quality depends on the quality of agent descriptions and system prompt

For more information, see the [Classifier Overview](/agent-squad/classifier/overview) and [Agents](/agent-squad/agents/overview) documentation.