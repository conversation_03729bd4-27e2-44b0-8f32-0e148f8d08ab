---
title: Quickstart
---
import { Tabs, TabItem } from '@astrojs/starlight/components';


# Quickstart Guide for Agent Squad

To help you kickstart with the Agent Squad framework, we'll walk you through the step-by-step process of setting up and running your first multi-agent conversation.

<br/>

> 💁 Ensure you have Node.js and npm installed (for TypeScript) or Python installed (for Python) on your development environment before proceeding.

## Prerequisites

1. Create a new project:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```bash
    mkdir test_agent_squad
    cd test_agent_squad
    npm init
    ```
    Follow the steps to generate a `package.json` file.
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```bash
    mkdir test_agent_squad
    cd test_agent_squad
    # Optional: Set up a virtual environment
    python -m venv venv
    source venv/bin/activate  # On Windows use `venv\Scripts\activate`
    ```
  </TabItem>
</Tabs>

2. Authenticate with your AWS account

This quickstart demonstrates the use of Amazon Bedrock for both classification and agent responses.

To authenticate with your AWS account, follow these steps:

a. Install the AWS CLI if you haven't already. You can download it from the [official AWS CLI page](https://aws.amazon.com/cli/).

b. Configure your AWS CLI with your credentials. For detailed instructions on how to set up your AWS CLI, please refer to the [AWS CLI Configuration Quickstart Guide](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-quickstart.html).

c. After configuring your AWS CLI, verify your authentication by running:

```bash
aws sts get-caller-identity
```

If successful, this command will return your AWS account ID, user ID, and ARN.

By default, the framework is configured as follows:
  - Classifier: Uses the **[Bedrock Classifier](/agent-squad/classifiers/built-in/bedrock-classifier/)** implementation with `anthropic.claude-3-5-sonnet-********-v1:0`
  - Agent: Utilizes the **[Bedrock LLM Agent](/agent-squad/agents/built-in/bedrock-llm-agent)** with `anthropic.claude-3-haiku-********-v1:0`

<br/>

> **Important**
>
>   These are merely default settings and can be easily changed to suit your needs or preferences.

<br/>

You have the flexibility to:
  - Change the classifier model or implementation
  - Change the agent model or implementation
  - Use any other compatible models available through Amazon Bedrock

Ensure you have [requested access](https://docs.aws.amazon.com/bedrock/latest/userguide/model-access.html) to the models you intend to use through the AWS console.

<br/>

> **To customize the model selection**:
>   - For the classifier, refer to [our guide](/agent-squad/classifiers/overview) on configuring the classifier.
>   - For the agent, refer to our guide on configuring [agents](/agent-squad/agents/overview).

## 🚀 Get Started!

1. Install the Agent Squad framework in your project:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```bash
    npm install agent-squad
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```bash
    pip install "agent-squad[anthropic]" # for Anthropic classifier and agent
    pip install "agent-squad[openai]" # for OpenAI classifier and agent
    pip install "agent-squad[all]" # for all packages including Anthropic and OpenAI
    ```
  </TabItem>
</Tabs>

2. Create a new file for your quickstart code:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    Create a file named `quickstart.ts`.
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    Create a file named `quickstart.py`.
  </TabItem>
</Tabs>

3. Create an Orchestrator:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    import { AgentSquad } from "agent-squad";
    const orchestrator = new AgentSquad({
      config: {
        LOG_AGENT_CHAT: true,
        LOG_CLASSIFIER_CHAT: true,
        LOG_CLASSIFIER_RAW_OUTPUT: false,
        LOG_CLASSIFIER_OUTPUT: true,
        LOG_EXECUTION_TIMES: true,
      }
    });
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    import uuid
    import asyncio
    from typing import Optional, List, Dict, Any
    import json
    import sys
    from agent_squad.orchestrator import AgentSquad, AgentSquadConfig
    from agent_squad.agents import (BedrockLLMAgent,
     BedrockLLMAgentOptions,
     AgentResponse,
     AgentCallbacks)
    from agent_squad.types import ConversationMessage, ParticipantRole

    orchestrator = AgentSquad(options=AgentSquadConfig(
      LOG_AGENT_CHAT=True,
      LOG_CLASSIFIER_CHAT=True,
      LOG_CLASSIFIER_RAW_OUTPUT=True,
      LOG_CLASSIFIER_OUTPUT=True,
      LOG_EXECUTION_TIMES=True,
      MAX_RETRIES=3,
      USE_DEFAULT_AGENT_IF_NONE_IDENTIFIED=True,
      MAX_MESSAGE_PAIRS_PER_AGENT=10
    ))
    ```
  </TabItem>
</Tabs>

4. Add Agents:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    import { BedrockLLMAgent } from "agent-squad";
    orchestrator.addAgent(
      new BedrockLLMAgent({
        name: "Tech Agent",
        description: "Specializes in technology areas including software development, hardware, AI, cybersecurity, blockchain, cloud computing, emerging tech innovations, and pricing/costs related to technology products and services.",
        streaming: true
      })
    );

    orchestrator.addAgent(
      new BedrockLLMAgent({
        name: "Health Agent",
        description: "Focuses on health and medical topics such as general wellness, nutrition, diseases, treatments, mental health, fitness, healthcare systems, and medical terminology or concepts.",
      })
    );
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    class BedrockLLMAgentCallbacks(AgentCallbacks):
        def on_llm_new_token(self, token: str) -> None:
            # handle response streaming here
            print(token, end='', flush=True)

    tech_agent = BedrockLLMAgent(BedrockLLMAgentOptions(
      name="Tech Agent",
      streaming=True,
      description="Specializes in technology areas including software development, hardware, AI, \
      cybersecurity, blockchain, cloud computing, emerging tech innovations, and pricing/costs \
      related to technology products and services.",
      callbacks=BedrockLLMAgentCallbacks()
    ))
    orchestrator.add_agent(tech_agent)

    health_agent = BedrockLLMAgent(BedrockLLMAgentOptions(
      name="Health Agent",
      description="Focuses on health and medical topics such as general wellness, nutrition, diseases, treatments, mental health, fitness, healthcare systems, and medical terminology or concepts.",
      callbacks=BedrockLLMAgentCallbacks()
    ))
    orchestrator.add_agent(health_agent)
    ```
  </TabItem>
</Tabs>

5. Send a Query:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    const userId = "quickstart-user";
    const sessionId = "quickstart-session";
    const query = "What are the latest trends in AI?";
    console.log(`\nUser Query: ${query}`);

    async function main() {
      try {
        const response = await orchestrator.routeRequest(query, userId, sessionId);
        console.log("\n** RESPONSE ** \n");
        console.log(`> Agent ID: ${response.metadata.agentId}`);
        console.log(`> Agent Name: ${response.metadata.agentName}`);
        console.log(`> User Input: ${response.metadata.userInput}`);
        console.log(`> User ID: ${response.metadata.userId}`);
        console.log(`> Session ID: ${response.metadata.sessionId}`);
        console.log(
          `> Additional Parameters:`,
          response.metadata.additionalParams
        );
        console.log(`\n> Response: `);
        // Stream the content
        for await (const chunk of response.output) {
          if (typeof chunk === "string") {
            process.stdout.write(chunk);
          } else {
            console.error("Received unexpected chunk type:", typeof chunk);
          }
        }
        console.log();
      } catch (error) {
        console.error("An error occurred:", error);
        // Here you could also add more specific error handling if needed
      }
    }

    main();
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    async def handle_request(_orchestrator: AgentSquad, _user_input: str, _user_id: str, _session_id: str):
        response: AgentResponse = await _orchestrator.route_request(_user_input, _user_id, _session_id)
        # Print metadata
        print("\nMetadata:")
        print(f"Selected Agent: {response.metadata.agent_name}")
        if response.streaming:
            print('Response:', response.output.content[0]['text'])
        else:
            print('Response:', response.output.content[0]['text'])

    if __name__ == "__main__":
        USER_ID = "user123"
        SESSION_ID = str(uuid.uuid4())
        print("Welcome to the interactive Multi-Agent system. Type 'quit' to exit.")
        while True:
            # Get user input
            user_input = input("\nYou: ").strip()
            if user_input.lower() == 'quit':
                print("Exiting the program. Goodbye!")
                sys.exit()
            # Run the async function
            asyncio.run(handle_request(orchestrator, user_input, USER_ID, SESSION_ID))
    ```
  </TabItem>
</Tabs>

Now, let's run the quickstart script:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```bash
    npx ts-node quickstart.ts
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```bash
    python quickstart.py
    ```
  </TabItem>
</Tabs>

Congratulations! 🎉
You've successfully set up and run your first multi-agent conversation using the Agent Squad System.

## 👨‍💻 Next Steps

Now that you've seen the basic functionality, here are some next steps to explore:

1. Try adding other agents from those built-in in the framework ([Bedrock LLM Agent](/agent-squad/agents/built-in/bedrock-llm-agent), [Amazon Lex Bot](/agent-squad/agents/built-in/lex-bot-agent), [Amazon Bedrock Agent](/agent-squad/agents/built-in/amazon-bedrock-agent), [Lambda Agent](/agent-squad/agents/built-in/lambda-agent), [OpenAI Agent](/agent-squad/agents/built-in/openai-agent)).
2. Experiment with different storage options, such as [Amazon DynamoDB](/agent-squad/storage/dynamodb) for persistent storage.
3. Explore the [Agent Overlap Analysis](/agent-squad/cookbook/monitoring/agent-overlap/) feature to optimize your agent configurations.
4. Integrate the system into a web application or deploy it as an [AWS Lambda function](/agent-squad/deployment/aws-lambda).
5. Try adding your own [custom agents](/agent-squad/agents/custom-agents) by extending the `Agent` class.

For more detailed information on these advanced features, check out our full documentation.

## 🧹 Cleanup

As this quickstart uses in-memory storage and local resources, there's no cleanup required. Simply stop the script when you're done experimenting.
