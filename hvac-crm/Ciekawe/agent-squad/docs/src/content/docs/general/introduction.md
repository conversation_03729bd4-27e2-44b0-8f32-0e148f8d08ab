---
title: Introduction
description: Introduction to Agent Squad framework
---

The emergence of both large and small language models, deployable in cloud environments or on local systems, offers the opportunity to utilize multiple specialized models for specific tasks.

When configured to operate independently on designated tasks, these specialized models are typically referred to as **agents**.

Building intelligent, context-aware AI applications faces a significant challenge in managing a diverse set of agents. This core difficulty is compounded by the need to unify operations across different domains, maintain contextual understanding, and implement scalable architectures.

## 🚀 Building flexible AI systems

To address these challenges and empower developers to quickly experiment with and deploy advanced multi-agent AI systems, we've created the **Agent Squad** framework.

The Agent Squad is a flexible and powerful framework designed for managing multiple AI agents, intelligently routing user queries, and handling complex conversations. Built with scalability and modularity in mind, it allows to create AI applications that can maintain coherent dialogues across multiple domains, efficiently delegating tasks to specialized agents while preserving context throughout the interaction.

This project has been designed to address a wide array of use-cases, including but not limited to:
- Complex customer support systems
- Multi-domain virtual assistants
- Smart home and IoT device management
- Multi-lingual customer support


## 🔖 Features

Below are some of the key features we've built into the Agent Squad framework:

- 🧠 **Intelligent Intent Classification** — Dynamically route queries to the most suitable agent based on context and content.
- 🌊 **Flexible Agent Responses** — Support for both **streaming** and **non-streaming** responses from different agents.
- 📚 **Context Management** — Maintain and utilize conversation context across multiple agents for coherent interactions.
- 🔧 **Extensible Architecture** — Easily integrate new agents or customize existing ones to fit your specific needs.
- 🌐 **Universal Deployment** — Run anywhere - from AWS Lambda to your local environment or any cloud platform.
- 🚀 **Scalable Design** — Handle multiple concurrent conversations efficiently, scaling from simple chatbots to complex AI systems.
- 📊 **Agent Overlap Analysis** — Built-in tools to analyze and optimize your agent configurations.
- 📦 **Pre-configured Agents** — Ready-to-use agents powered by Amazon Bedrock models.

With the Agent Squad framework, developers can rapidly prototype and deploy sophisticated AI conversation systems that leverage the power of multiple specialized agents.

The framework's extensibility and customization capabilities support the creation of a wide range of AI applications, from complex customer service systems to multi-domain virtual assistants and advanced collaborative AI tools, allowing for the implementation of diverse ideas.