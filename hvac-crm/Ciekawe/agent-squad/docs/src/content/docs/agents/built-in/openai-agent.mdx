---
title: Open AI Agent
description: Documentation for the OpenAI Agent
---

The `OpenAIAgent` is a powerful agent class in the Agent Squad framework that integrates with OpenAI's Chat Completion API. This agent allows you to leverage OpenAI's language models for various natural language processing tasks.

## Key Features

- Integration with OpenAI's Chat Completion API
- Support for multiple OpenAI models (e.g., GPT-4, GPT-3.5)
- Streaming and non-streaming response options
- Customizable inference configuration
- Conversation history handling for context-aware responses
- Customizable system prompts with variable support
- Support for retrievers to enhance responses with additional context
- Flexible initialization with API key or custom client

## Configuration Options

The `OpenAIAgentOptions` extends the base `AgentOptions` with the following fields:

### Required Fields
- `name`: Name of the agent
- `description`: Description of the agent's capabilities
- Authentication (one of the following is required):
  - `apiKey`: Your OpenAI API key
  - `client`: Custom OpenAI client instance

### Optional Fields
- `model`: OpenAI model identifier (e.g., 'gpt-4', 'gpt-3.5-turbo'). Defaults to `OPENAI_MODEL_ID_GPT_O_MINI`
- `streaming`: Enable streaming responses. Defaults to `false`
- `retriever`: Custom retriever instance for enhancing responses with additional context
- `inferenceConfig`: Configuration for model inference:
  - `maxTokens`: Maximum tokens to generate (default: 1000)
  - `temperature`: Controls randomness (0-1)
  - `topP`: Controls diversity via nucleus sampling
  - `stopSequences`: Sequences that stop generation
- `customSystemPrompt`: System prompt configuration:
  - `template`: Template string with optional variable placeholders
  - `variables`: Key-value pairs for template variables

## Creating an OpenAIAgent

### Python Package

If you haven't already installed the OpenAI-related dependencies, make sure to install them:

```bash
pip install "agent-squad[openai]"
```

Here are various examples showing different ways to create and configure an OpenAIAgent:

### Basic Examples

**1. Minimal Configuration**

import { Tabs, TabItem } from '@astrojs/starlight/components';


<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'A versatile AI assistant',
  apiKey: 'your-openai-api-key'
});
```
</TabItem>
  <TabItem label="Python" icon="seti:python">

```python
agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='A versatile AI assistant',
    api_key='your-openai-api-key'
))
```
  </TabItem>
</Tabs>

<hr/>

**2. Using Custom Client**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
import OpenAI from 'openai';
const customClient = new OpenAI({ apiKey: 'your-openai-api-key' });
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'A versatile AI assistant',
  client: customClient
});
```
</TabItem>
  <TabItem label="Python" icon="seti:python">
```python
from openai import OpenAI

custom_client = OpenAI(api_key='your-openai-api-key')

agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='A versatile AI assistant',
    client=custom_client
))
```
</TabItem>
</Tabs>


<hr/>

**3. Custom Model and Streaming**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'A streaming-enabled assistant',
  apiKey: 'your-openai-api-key',
  model: 'gpt-4',
  streaming: true
});
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='A streaming-enabled assistant',
    api_key='your-openai-api-key',
    model='gpt-4',
    streaming=True
))
```
  </TabItem>
</Tabs>

<hr/>


**4. With Inference Configuration**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'An assistant with custom inference settings',
  apiKey: 'your-openai-api-key',
  inferenceConfig: {
    maxTokens: 500,
    temperature: 0.7,
    topP: 0.9,
    stopSequences: ['Human:', 'AI:']
  }
});
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='An assistant with custom inference settings',
    api_key='your-openai-api-key',
    inference_config={
        'maxTokens': 500,
        'temperature': 0.7,
        'topP': 0.9,
        'stopSequences': ['Human:', 'AI:']
    }
))
```
  </TabItem>
</Tabs>

<hr/>

**5. With Simple System Prompt**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'An assistant with custom prompt',
  apiKey: 'your-openai-api-key',
  customSystemPrompt: {
    template: 'You are a helpful AI assistant focused on technical support.'
  }
});
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='An assistant with custom prompt',
    api_key='your-openai-api-key',
    custom_system_prompt={
        'template': 'You are a helpful AI assistant focused on technical support.'
    }
))
```
  </TabItem>
</Tabs>

<hr/>

**6. With System Prompt Variables**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'An assistant with variable prompt',
  apiKey: 'your-openai-api-key',
  customSystemPrompt: {
    template: 'You are an AI assistant specialized in {{DOMAIN}}. Always use a {{TONE}} tone.',
    variables: {
      DOMAIN: 'customer support',
      TONE: 'friendly and helpful'
    }
  }
});
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='An assistant with variable prompt',
    api_key='your-openai-api-key',
    custom_system_prompt={
        'template': 'You are an AI assistant specialized in {{DOMAIN}}. Always use a {{TONE}} tone.',
        'variables': {
            'DOMAIN': 'customer support',
            'TONE': 'friendly and helpful'
        }
    }
))
```
  </TabItem>
</Tabs>

<hr/>

**7. With Custom Retriever**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const retriever = new CustomRetriever({
  // Retriever configuration
});
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'An assistant with retriever',
  apiKey: 'your-openai-api-key',
  retriever: retriever
});
```
</TabItem>
  <TabItem label="Python" icon="seti:python">
```python
retriever = CustomRetriever(
    # Retriever configuration
)

agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='An assistant with retriever',
    api_key='your-openai-api-key',
    retriever=retriever
))
```
</TabItem>
</Tabs>

<hr/>

**8. Combining Multiple Options**

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const agent = new OpenAIAgent({
  name: 'OpenAI Assistant',
  description: 'An assistant with multiple options',
  apiKey: 'your-openai-api-key',
  model: 'gpt-4',
  streaming: true,
  inferenceConfig: {
    maxTokens: 500,
    temperature: 0.7
  },
  customSystemPrompt: {
    template: 'You are an AI assistant specialized in {{DOMAIN}}.',
    variables: {
      DOMAIN: 'technical support'
    }
  }
});
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
agent = OpenAIAgent(OpenAIAgentOptions(
    name='OpenAI Assistant',
    description='An assistant with multiple options',
    api_key='your-openai-api-key',
    model='gpt-4',
    streaming=True,
    inference_config={
        'maxTokens': 500,
        'temperature': 0.7
    },
    custom_system_prompt={
        'template': 'You are an AI assistant specialized in {{DOMAIN}}.',
        'variables': {
            'DOMAIN': 'technical support'
        }
    }
))
```
  </TabItem>
</Tabs>

<hr/>

**9. Complete Example with All Options**

Here's a comprehensive example showing all available configuration options:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
import { OpenAIAgent } from 'agent-squad';

const agent = new OpenAIAgent({
  // Required fields
  name: 'Advanced OpenAI Assistant',
  description: 'A fully configured AI assistant powered by OpenAI models',
  apiKey: 'your-openai-api-key',

  // Optional fields
  model: 'gpt-4',  // Choose OpenAI model
  streaming: true, // Enable streaming responses
  retriever: customRetriever, // Custom retriever for additional context

  // Inference configuration
  inferenceConfig: {
    maxTokens: 500,      // Maximum tokens to generate
    temperature: 0.7,    // Control randomness (0-1)
    topP: 0.9,          // Control diversity via nucleus sampling
    stopSequences: ['Human:', 'AI:']  // Sequences that stop generation
  },

  // Custom system prompt with variables
  customSystemPrompt: {
    template: `You are an AI assistant specialized in {{DOMAIN}}.
              Your core competencies:
              {{SKILLS}}

              Communication style:
              - Maintain a {{TONE}} tone
              - Focus on {{FOCUS}}
              - Prioritize {{PRIORITY}}`,
    variables: {
      DOMAIN: 'scientific research',
      SKILLS: [
        '- Advanced data analysis',
        '- Statistical methodology',
        '- Research design',
        '- Technical writing'
      ],
      TONE: 'professional and academic',
      FOCUS: 'accuracy and clarity',
      PRIORITY: 'evidence-based insights'
    }
  }
});
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
from agent_squad import OpenAIAgent, OpenAIAgentOptions

agent = OpenAIAgent(OpenAIAgentOptions(
    # Required fields
    name='Advanced OpenAI Assistant',
    description='A fully configured AI assistant powered by OpenAI models',
    api_key='your-openai-api-key',

    # Optional fields
    model='gpt-4',         # Choose OpenAI model
    streaming=True,        # Enable streaming responses
    retriever=custom_retriever,  # Custom retriever for additional context

    # Inference configuration
    inference_config={
        'maxTokens': 500,     # Maximum tokens to generate
        'temperature': 0.7,   # Control randomness (0-1)
        'topP': 0.9,         # Control diversity via nucleus sampling
        'stopSequences': ['Human:', 'AI:']  # Sequences that stop generation
    },

    # Custom system prompt with variables
    custom_system_prompt={
        'template': """You are an AI assistant specialized in {{DOMAIN}}.
                      Your core competencies:
                      {{SKILLS}}

                      Communication style:
                      - Maintain a {{TONE}} tone
                      - Focus on {{FOCUS}}
                      - Prioritize {{PRIORITY}}""",
        'variables': {
            'DOMAIN': 'scientific research',
            'SKILLS': [
                '- Advanced data analysis',
                '- Statistical methodology',
                '- Research design',
                '- Technical writing'
            ],
            'TONE': 'professional and academic',
            'FOCUS': 'accuracy and clarity',
            'PRIORITY': 'evidence-based insights'
        }
    }
))
```
  </TabItem>
</Tabs>

## Using the OpenAIAgent

There are two ways to use the OpenAIAgent: directly or through the Agent Squad.

### Direct Usage

Call the agent directly when you want to use a single agent without orchestrator routing:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const classifierResult = {
  selectedAgent: agent,
  confidence: 1.0
};

const response = await orchestrator.agentProcessRequest(
  "What is the capital of France?",
  "user123",
  "session456",
  classifierResult
);
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
classifier_result = ClassifierResult(selected_agent=agent, confidence=1.0)

response = await orchestrator.agent_process_request(
    "What is the capital of France?",
    "user123",
    "session456",
    classifier_result
)
```
  </TabItem>
</Tabs>

### Using with the Orchestrator

Add the agent to Agent Squad for use in a multi-agent system:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
```typescript
const orchestrator = new AgentSquad();
orchestrator.addAgent(agent);

const response = await orchestrator.routeRequest(
  "What is the capital of France?",
  "user123",
  "session456"
);
```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
```python
orchestrator = AgentSquad()
orchestrator.add_agent(agent)

response = await orchestrator.route_request(
    "What is the capital of France?",
    "user123",
    "session456"
)
```
  </TabItem>
</Tabs>

