---
title: LexBotAgent
description: Documentation for the LexBotAgent in the Agent Squad System
---

The `LexBotAgent` is a specialized agent class in the Agent Squad System that integrates [Amazon Lex bots](https://aws.amazon.com/lex/).

## Key Features

- Seamless integration with Amazon Lex V2 bots
- Support for multiple locales
- Easy configuration with bot ID and alias

## Creating a LexBotAgent

### Python Package

If you haven't already installed the AWS-related dependencies, make sure to install them:

```bash
pip install "agent-squad[aws]"
```

To create a new `LexBotAgent` with the required parameters, use the following code:

import { Tabs, TabItem } from '@astrojs/starlight/components';

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    import { LexBotAgent } from 'agent-squad';

    const agent = new LexBotAgent({
      name: 'My Basic Lex Bot Agent',
      description: 'An agent specialized in flight booking',
      botId: 'your-bot-id',
      botAliasId: 'your-bot-alias-id',
      localeId: 'en_US',
      region: 'us-east-1'
    });
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    from agent_squad.agents import LexBotAgent, LexBotAgentOptions

    agent = LexBotAgent(LexBotAgentOptions(
        name='My Basic Lex Bot Agent',
        description='An agent specialized in flight booking',
        bot_id='your-bot-id',
        bot_alias_id='your-bot-alias-id',
        locale_id='en_US',
        region='us-east-1'
    ))
    ```
  </TabItem>
</Tabs>

### Parameter Explanations

- `name`: (Required) Identifies the agent within your system.
- `description`: (Required) Describes the agent's purpose or capabilities.
- `bot_id`: (Required) The ID of the Amazon Lex bot you want to use.
- `bot_alias_id`: (Required) The alias ID of the Amazon Lex bot.
- `locale_id`: (Required) The locale ID for the bot (e.g., 'en_US').
- `region`: (Optional) The AWS region where the Lex bot is deployed. If not provided, it will use the `AWS_REGION` environment variable or default to 'us-east-1'.

## Adding the Agent to the Orchestrator

To integrate the LexBotAgent into your Agent Squad, follow these steps:

1. First, ensure you have created an instance of the orchestrator:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    import { AgentSquad } from 'agent-squad';

    const orchestrator = new AgentSquad();
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    from agent_squad.orchestrator import AgentSquad

    orchestrator = AgentSquad()
    ```
  </TabItem>
</Tabs>

2. Then, add the LexBotAgent to the orchestrator:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    orchestrator.addAgent(agent);
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    orchestrator.add_agent(agent)
    ```
  </TabItem>
</Tabs>

3. Now you can use the orchestrator to route requests to the appropriate agent, including your Lex bot:

<Tabs syncKey="runtime">
  <TabItem label="TypeScript" icon="seti:typescript" color="blue">
    ```typescript
    const response = await orchestrator.routeRequest(
      "I would like to book a flight",
      "user123",
      "session456"
    );
    ```
  </TabItem>
  <TabItem label="Python" icon="seti:python">
    ```python
    response = await orchestrator.route_request(
        "I would like to book a flight",
        "user123",
        "session456"
    )
    ```
  </TabItem>
</Tabs>


---

By leveraging the `LexBotAgent`, you can easily integrate **pre-built Amazon Lex Bots** into your Agent Squad.