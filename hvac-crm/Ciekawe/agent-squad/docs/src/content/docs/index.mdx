---
title: Agent Squad framework
description: Manage multiple AI agents and handle complex conversations
template: splash
hero:
  tagline: Flexible and powerful framework for managing multiple AI agents and handling complex conversations 🤖🚀
  actions:
    - text: How it works
      link: /agent-squad/general/how-it-works
      icon: right-arrow
      variant: primary
    # Visual break - Next line starts here
    - text: GitHub Repository
      link: https://github.com/awslabs/agent-squad
      icon: external
      variant: minimal
    - text: NPM Repository
      link: https://www.npmjs.com/package/agent-squad
      icon: external
      variant: minimal
    - text: PyPI Repository
      link: https://pypi.org/project/agent-squad/
      icon: external
      variant: minimal
---


<Card title="Looking for details on Amazon Bedrock’s multi-agent collaboration capability announced during <PERSON>'s keynote at re:Invent 2024?">
Visit the [Amazon Bedrock Agents](https://aws.amazon.com/bedrock/agents/) page to explore how multi-agent collaboration enables developers to build, deploy, and manage specialized agents designed for tackling complex workflows efficiently and accurately.
</Card>


import { Card, CardGrid } from '@astrojs/starlight/components';
import { Badge } from '@astrojs/starlight/components';

## Key Features

- **Multi-Agent Orchestration**: Seamlessly coordinate and leverage multiple AI agents in a single system
- **Dual language support**: Fully implemented in both **Python** and **TypeScript**
- **Intelligent intent classification**: Dynamically route queries to the most suitable agent based on context and content
- **Flexible agent responses**: Support for both streaming and non-streaming responses from different agents
- **Context management**: Maintain and utilize conversation context across multiple agents for coherent interactions
- **Extensible architecture**: Easily integrate new agents or customize existing ones to fit your specific needs
- **Universal deployment**: Run anywhere - from AWS Lambda to your local environment or any cloud platform




<CardGrid>
  <Card title="Quick Installation" icon="rocket">
    Get up and running in minutes:
    See our [Quick Start Guide](/agent-squad/general/quickstart) for more details.
  </Card>
  <Card title="How it works" icon="rocket">
    See our [How it works](/agent-squad/general/how-it-works) for more details.
  </Card>
  <Card title="Code Samples & Deployment" icon="rocket">
    Explore our code samples and deployment options:
    - [Local Development Guide](/agent-squad/cookbook/examples/typescript-local-demo)
    - [AWS Lambda Deployment (TypeScript)](/agent-squad/cookbook/lambda/aws-lambda-nodejs)
    - [AWS Lambda Deployment (Python)](/agent-squad/cookbook/lambda/aws-lambda-python)
    - [Chainlit (Python)](/agent-squad/cookbook/examples/chat-chainlit-app)
    - [FastAPI streaming (Python)](/agent-squad/cookbook/examples/fast-api-streaming)
  </Card>
  <Card title="Powerful Agents" icon="puzzle">
    Discover our built-in agents:
    - <Badge text="New" variant="note" size="small"/>[Supervisor Agent](/agent-squad/agents/built-in/supervisor-agent)
    - <Badge text="New" variant="note" size="small"/>[Open AI Agent](/agent-squad/agents/built-in/openai-agent)
    - <Badge text="New" variant="note" size="small"/>[Bedrock Inline Agent](/agent-squad/agents/built-in/bedrock-inline-agent)
    - <Badge text="New" variant="note" size="small"/>[Bedrock Flows Agent](/agent-squad/agents/built-in/bedrock-flows-agent)
    - [Bedrock LLM Agent](/agent-squad/agents/built-in/bedrock-llm-agent)
    - [Amazon Bedrock Agent](/agent-squad/agents/built-in/amazon-bedrock-agent)
    - [Lex Bot Agent](/agent-squad/agents/built-in/lex-bot-agent)
    - [AWS Lambda Agent](/agent-squad/agents/built-in/lambda-agent)
    - [Bedrock Translator Agent](/agent-squad/agents/built-in/bedrock-translator-agent)
    - [Comprehend Filter Agent](/agent-squad/agents/built-in/comprehend-filter-agent)
    - [Chain Agent](/agent-squad/agents/built-in/chain-agent)
    Learn how to [create your own custom agents](/agent-squad/agents/custom-agents).
  </Card>
</CardGrid>

