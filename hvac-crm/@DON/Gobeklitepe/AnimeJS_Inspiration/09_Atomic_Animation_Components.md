# Atomic Animation Components

## Core Concept
Create a library of reusable, composable animation components that follow atomic design principles, enabling consistent, maintainable animations throughout the HVAC CRM system.

## Inspiration from Anime.js
Anime.js offers a modular approach to animation with composable functions and timeline orchestration. These techniques can be adapted to GSAP to create a structured animation system that aligns with atomic design methodology.

## Implementation Guidelines

### 1. Animation Atoms
- Create fundamental animation building blocks:
  - Fades (in/out with various durations)
  - Movements (directional with consistent distances)
  - Scales (up/down with consistent ratios)
  - Rotations (with standard angles)
  - Highlights (attention-grabbing pulses and glows)
- Standardize timing and easing for consistent feel
- Document each atom with usage guidelines

### 2. Animation Molecules
- Combine atoms into meaningful animation patterns:
  - Entrances (fade+scale, slide+fade)
  - Exits (fade+scale, slide+fade)
  - Transitions (crossfades, morphs)
  - Attention-getters (bounce+highlight, shake+glow)
  - Status changes (success, error, warning, info)
- Create consistent naming conventions
- Ensure molecules maintain atomic properties for customization

### 3. Animation Organisms
- Build complex animation sequences for specific contexts:
  - Page transitions
  - Modal dialogs
  - Data loading states
  - Form submissions
  - Notification systems
- Create composition patterns for combining molecules
- Document usage contexts and variations

### 4. Animation Templates and Pages
- Design animation systems for entire views:
  - Dashboard animation patterns
  - Service order workflow animations
  - Customer profile view animations
  - Settings page animations
- Create consistent entry/exit patterns for major sections
- Document relationships between animations across views

## Technical Implementation

```javascript
// Example of implementing atomic animation components with GSAP
// Inspired by Anime.js modular approach

// Animation Atoms
const AnimationAtoms = {
  // Fade animations
  fadeIn: (target, duration = 0.3, ease = "power2.out") => {
    return gsap.to(target, {
      opacity: 1,
      duration,
      ease
    });
  },
  
  fadeOut: (target, duration = 0.3, ease = "power2.in") => {
    return gsap.to(target, {
      opacity: 0,
      duration,
      ease
    });
  },
  
  // Movement animations
  slideInRight: (target, distance = 50, duration = 0.5, ease = "power2.out") => {
    gsap.set(target, { x: distance });
    return gsap.to(target, {
      x: 0,
      duration,
      ease
    });
  },
  
  slideOutLeft: (target, distance = 50, duration = 0.5, ease = "power2.in") => {
    return gsap.to(target, {
      x: -distance,
      duration,
      ease
    });
  },
  
  // Scale animations
  scaleUp: (target, from = 0.8, to = 1, duration = 0.4, ease = "back.out(1.7)") => {
    gsap.set(target, { scale: from });
    return gsap.to(target, {
      scale: to,
      duration,
      ease
    });
  },
  
  scaleDown: (target, from = 1, to = 0.8, duration = 0.3, ease = "power2.in") => {
    gsap.set(target, { scale: from });
    return gsap.to(target, {
      scale: to,
      duration,
      ease
    });
  },
  
  // Attention animations
  pulse: (target, scale = 1.05, duration = 0.3, repeat = 1) => {
    return gsap.to(target, {
      scale: scale,
      duration: duration / 2,
      repeat: repeat * 2,
      yoyo: true,
      ease: "sine.inOut"
    });
  },
  
  shake: (target, intensity = 5, duration = 0.5, repeat = 2) => {
    return gsap.to(target, {
      x: `+=${intensity}`,
      duration: duration / (repeat * 4),
      repeat: repeat * 4,
      yoyo: true,
      ease: "none"
    });
  }
};

// Animation Molecules
const AnimationMolecules = {
  // Entrance animations
  fadeInUp: (target, distance = 30, duration = 0.5, stagger = 0) => {
    gsap.set(target, { y: distance, opacity: 0 });
    return gsap.to(target, {
      y: 0,
      opacity: 1,
      duration,
      stagger,
      ease: "power2.out"
    });
  },
  
  popIn: (target, duration = 0.5, stagger = 0) => {
    gsap.set(target, { scale: 0, opacity: 0 });
    return gsap.to(target, {
      scale: 1,
      opacity: 1,
      duration,
      stagger,
      ease: "back.out(1.7)"
    });
  },
  
  // Status change animations
  success: (target, duration = 0.7) => {
    const timeline = gsap.timeline();
    
    timeline.to(target, {
      backgroundColor: "rgba(82, 196, 26, 0.2)",
      borderColor: "#52c41a",
      duration: duration / 3
    }).to(target, {
      backgroundColor: "rgba(82, 196, 26, 0.1)",
      duration: duration / 3 * 2
    });
    
    return timeline;
  },
  
  error: (target, duration = 0.7) => {
    const timeline = gsap.timeline();
    
    timeline.to(target, {
      backgroundColor: "rgba(255, 77, 79, 0.2)",
      borderColor: "#ff4d4f",
      duration: duration / 3
    }).add(AnimationAtoms.shake(target, 3, duration / 3 * 2, 1), "<");
    
    return timeline;
  },
  
  // Transition animations
  crossFade: (fromTarget, toTarget, duration = 0.5) => {
    const timeline = gsap.timeline();
    
    timeline.add(AnimationAtoms.fadeOut(fromTarget, duration / 2))
            .add(AnimationAtoms.fadeIn(toTarget, duration / 2), "-=0.1");
    
    return timeline;
  }
};

// Animation Organisms
const AnimationOrganisms = {
  // Modal dialog animations
  openModal: (modalContainer, modalContent, overlay, duration = 0.6) => {
    const timeline = gsap.timeline();
    
    // Show container
    gsap.set(modalContainer, { display: "flex" });
    
    // Animate overlay
    timeline.add(AnimationAtoms.fadeIn(overlay, duration / 2));
    
    // Animate content
    timeline.add(AnimationMolecules.popIn(modalContent, duration / 2), "-=0.1");
    
    return timeline;
  },
  
  closeModal: (modalContainer, modalContent, overlay, duration = 0.5) => {
    const timeline = gsap.timeline({
      onComplete: () => gsap.set(modalContainer, { display: "none" })
    });
    
    // Animate content
    timeline.add(AnimationAtoms.scaleDown(modalContent, 1, 0.9, duration / 2));
    timeline.add(AnimationAtoms.fadeOut(modalContent, duration / 2), "<");
    
    // Animate overlay
    timeline.add(AnimationAtoms.fadeOut(overlay, duration / 2), "<+=0.1");
    
    return timeline;
  },
  
  // Form submission animations
  formSubmission: (form, submitButton, successMessage, duration = 1) => {
    const timeline = gsap.timeline();
    
    // Disable form
    gsap.set(form.querySelectorAll('input, select, textarea'), { pointerEvents: "none" });
    
    // Animate submit button
    timeline.add(AnimationAtoms.pulse(submitButton, 0.95, duration / 5, 1));
    
    // Show loading state
    timeline.to(submitButton, {
      width: submitButton.offsetHeight,
      duration: duration / 5,
      ease: "power2.inOut"
    });
    
    // Success state (assuming successful submission)
    timeline.to(submitButton, {
      width: "auto",
      backgroundColor: "#52c41a",
      duration: duration / 5,
      ease: "power2.inOut",
      delay: duration / 5 * 2 // Simulating server processing time
    });
    
    // Show success message
    timeline.add(AnimationMolecules.fadeInUp(successMessage, 20, duration / 5));
    
    return timeline;
  }
};

// Animation Templates
const AnimationTemplates = {
  // Page transition template
  pageTransition: (currentPage, nextPage, direction = "right", duration = 0.8) => {
    const timeline = gsap.timeline();
    
    // Set initial states
    gsap.set(nextPage, { 
      display: "block", 
      opacity: 0, 
      x: direction === "right" ? "100%" : "-100%" 
    });
    
    // Animate current page out
    timeline.to(currentPage, {
      opacity: 0,
      x: direction === "right" ? "-30%" : "30%",
      duration: duration / 2,
      ease: "power2.in"
    });
    
    // Animate next page in
    timeline.to(nextPage, {
      opacity: 1,
      x: "0%",
      duration: duration / 2,
      ease: "power2.out"
    });
    
    // Cleanup
    timeline.add(() => {
      gsap.set(currentPage, { display: "none" });
    });
    
    return timeline;
  },
  
  // Dashboard loading template
  dashboardLoad: (dashboard, duration = 1.2) => {
    const timeline = gsap.timeline();
    
    // Select dashboard components
    const header = dashboard.querySelector('.dashboard-header');
    const sidebar = dashboard.querySelector('.dashboard-sidebar');
    const widgets = dashboard.querySelectorAll('.dashboard-widget');
    const charts = dashboard.querySelectorAll('.chart-container');
    
    // Animate header
    timeline.add(AnimationMolecules.fadeInUp(header, 20, duration / 6));
    
    // Animate sidebar
    timeline.add(AnimationAtoms.slideInRight(sidebar, -30, duration / 6), "-=0.1");
    
    // Animate widgets with stagger
    timeline.add(AnimationMolecules.popIn(widgets, duration / 4, 0.1), "-=0.1");
    
    // Animate charts with stagger
    timeline.add(AnimationMolecules.fadeInUp(charts, 30, duration / 3, 0.15), "-=0.2");
    
    return timeline;
  }
};

// Usage example
function initializeAnimations() {
  // Set up modal animations
  const modals = document.querySelectorAll('.modal-container');
  
  modals.forEach(modal => {
    const modalContent = modal.querySelector('.modal-content');
    const modalOverlay = modal.querySelector('.modal-overlay');
    const closeButton = modal.querySelector('.modal-close');
    const openTrigger = document.querySelector(`[data-modal-target="${modal.id}"]`);
    
    if (openTrigger) {
      openTrigger.addEventListener('click', () => {
        AnimationOrganisms.openModal(modal, modalContent, modalOverlay);
      });
    }
    
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        AnimationOrganisms.closeModal(modal, modalContent, modalOverlay);
      });
    }
  });
  
  // Set up form animations
  const forms = document.querySelectorAll('form[data-animated]');
  
  forms.forEach(form => {
    const submitButton = form.querySelector('[type="submit"]');
    const successMessage = form.querySelector('.success-message');
    
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      AnimationOrganisms.formSubmission(form, submitButton, successMessage);
      
      // Simulate form processing
      setTimeout(() => {
        // Reset form for demo purposes
        form.reset();
        gsap.set(form.querySelectorAll('input, select, textarea'), { 
          pointerEvents: "auto" 
        });
        gsap.set(submitButton, {
          width: "auto",
          backgroundColor: ""
        });
        gsap.set(successMessage, { opacity: 0, y: 20 });
      }, 3000);
    });
  });
}
```

## Integration with HVAC CRM

This approach supports the "Projektowanie z Głębią" principle from Profound.md by creating a consistent, intuitive animation language throughout the system. Atomic animation components enable:

1. Consistent user experience across all parts of the application
2. Faster development through reusable animation components
3. Easier maintenance through standardized animation patterns
4. More intuitive interfaces through consistent motion design
5. Scalable animation system that can grow with the application

## Measurement of Success

- Reduced time to implement new animations
- Improved consistency in motion design across the application
- Enhanced user recognition of system patterns
- Reduced cognitive load through familiar animation patterns
- More efficient development process for UI components