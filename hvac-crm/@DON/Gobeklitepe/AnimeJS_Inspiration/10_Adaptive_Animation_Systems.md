# Adaptive Animation Systems

## Core Concept
Create animation systems that adapt to user preferences, device capabilities, and usage contexts, ensuring optimal performance and accessibility while maintaining expressive motion design.

## Inspiration from Anime.js
Anime.js offers flexible animation control with runtime parameter adjustments and conditional execution. These techniques can be adapted to GSAP to create animations that respond to user needs and system constraints.

## Implementation Guidelines

### 1. User Preference Adaptation
- Implement animation settings that respect user motion preferences
- Create reduced-motion alternatives for all animations
- Design animation intensity levels (subtle, moderate, expressive)
- Store and apply user animation preferences across sessions

### 2. Device Capability Detection
- Implement performance detection for automatic animation scaling
- Create tiered animation complexity based on device capabilities
- Design fallback animations for limited environments
- Implement progressive enhancement for animation features

### 3. Context-Aware Animation
- Adjust animation characteristics based on usage context:
  - Field mode: Simplified, high-contrast animations for outdoor use
  - Office mode: Full animation suite for desktop environments
  - Focus mode: Reduced animations for concentration-intensive tasks
  - Presentation mode: Enhanced animations for customer demonstrations
- Detect and adapt to bandwidth limitations for remote users

### 4. Accessibility-First Animation
- Ensure all animations have accessible alternatives
- Implement animation pausing during screen reader activity
- Create animations that enhance rather than hinder accessibility
- Design animations that support cognitive accessibility

## Technical Implementation

```javascript
// Example of implementing adaptive animation systems with GSAP
// Inspired by Anime.js approach to flexible animation control

// Animation Preference Manager
class AnimationPreferenceManager {
  constructor() {
    // Default preferences
    this.preferences = {
      motionIntensity: 'moderate', // 'minimal', 'subtle', 'moderate', 'expressive'
      reducedMotion: false,        // Respect prefers-reduced-motion
      animationsEnabled: true,     // Master toggle
      contextMode: 'auto'          // 'auto', 'field', 'office', 'focus', 'presentation'
    };
    
    // Initialize
    this.detectSystemPreferences();
    this.loadUserPreferences();
    this.setupListeners();
  }
  
  // Detect system-level preferences
  detectSystemPreferences() {
    // Check for reduced motion preference
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    this.preferences.reducedMotion = reducedMotionQuery.matches;
    
    // Check for device capabilities
    this.deviceCapabilities = {
      highPerformance: this.detectHighPerformanceDevice(),
      touchDevice: 'ontouchstart' in window,
      smallScreen: window.innerWidth < 768,
      lowBandwidth: navigator.connection ? navigator.connection.downlink < 1 : false
    };
    
    // Auto-detect context mode
    if (this.preferences.contextMode === 'auto') {
      if (this.deviceCapabilities.touchDevice && !this.isCharging() && this.deviceCapabilities.smallScreen) {
        this.preferences.contextMode = 'field';
      } else if (document.documentElement.classList.contains('focus-mode')) {
        this.preferences.contextMode = 'focus';
      } else {
        this.preferences.contextMode = 'office';
      }
    }
  }
  
  // Load user preferences from localStorage
  loadUserPreferences() {
    try {
      const savedPreferences = localStorage.getItem('animationPreferences');
      if (savedPreferences) {
        const parsed = JSON.parse(savedPreferences);
        this.preferences = { ...this.preferences, ...parsed };
      }
    } catch (error) {
      console.error('Error loading animation preferences:', error);
    }
  }
  
  // Save current preferences to localStorage
  saveUserPreferences() {
    try {
      localStorage.setItem('animationPreferences', JSON.stringify(this.preferences));
    } catch (error) {
      console.error('Error saving animation preferences:', error);
    }
  }
  
  // Set up event listeners for preference changes
  setupListeners() {
    // Listen for reduced motion preference changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    reducedMotionQuery.addEventListener('change', () => {
      this.preferences.reducedMotion = reducedMotionQuery.matches;
      this.applyPreferences();
    });
    
    // Listen for screen size changes
    window.addEventListener('resize', this.debounce(() => {
      this.deviceCapabilities.smallScreen = window.innerWidth < 768;
      if (this.preferences.contextMode === 'auto') {
        this.detectSystemPreferences();
        this.applyPreferences();
      }
    }, 250));
    
    // Listen for network changes
    if (navigator.connection) {
      navigator.connection.addEventListener('change', () => {
        this.deviceCapabilities.lowBandwidth = navigator.connection.downlink < 1;
        this.applyPreferences();
      });
    }
    
    // Listen for battery status changes
    this.setupBatteryListeners();
  }
  
  // Set up battery status listeners
  setupBatteryListeners() {
    if ('getBattery' in navigator) {
      navigator.getBattery().then(battery => {
        // Update on charging change
        battery.addEventListener('chargingchange', () => {
          if (this.preferences.contextMode === 'auto' && this.deviceCapabilities.touchDevice) {
            if (!battery.charging && this.deviceCapabilities.smallScreen) {
              this.preferences.contextMode = 'field';
            } else {
              this.preferences.contextMode = 'office';
            }
            this.applyPreferences();
          }
        });
      });
    }
  }
  
  // Check if device is charging
  isCharging() {
    if ('getBattery' in navigator) {
      return navigator.getBattery().then(battery => battery.charging);
    }
    return Promise.resolve(true); // Assume charging if API not available
  }
  
  // Detect if device is likely high performance
  detectHighPerformanceDevice() {
    // Simple heuristic based on available cores and memory
    const cores = navigator.hardwareConcurrency || 2;
    const memory = navigator.deviceMemory || 4;
    
    return cores >= 4 && memory >= 4;
  }
  
  // Apply current preferences to the document
  applyPreferences() {
    // Apply to document element as data attributes
    const html = document.documentElement;
    html.dataset.motionIntensity = this.preferences.motionIntensity;
    html.dataset.reducedMotion = this.preferences.reducedMotion;
    html.dataset.animationsEnabled = this.preferences.animationsEnabled;
    html.dataset.contextMode = this.preferences.contextMode;
    
    // Dispatch event for animation systems to respond
    window.dispatchEvent(new CustomEvent('animationpreferenceschanged', {
      detail: {
        preferences: this.preferences,
        capabilities: this.deviceCapabilities
      }
    }));
  }
  
  // Update a specific preference
  setPreference(key, value) {
    if (key in this.preferences) {
      this.preferences[key] = value;
      this.saveUserPreferences();
      this.applyPreferences();
    }
  }
  
  // Get current animation settings based on preferences and capabilities
  getAnimationSettings() {
    // Base settings
    const settings = {
      enabled: this.preferences.animationsEnabled,
      durationMultiplier: 1,
      staggerMultiplier: 1,
      complexAnimations: true,
      useTransforms: true,
      useOpacity: true
    };
    
    // Apply reduced motion settings
    if (this.preferences.reducedMotion) {
      settings.durationMultiplier = 0.5;
      settings.staggerMultiplier = 0.3;
      settings.complexAnimations = false;
    }
    
    // Apply motion intensity settings
    switch (this.preferences.motionIntensity) {
      case 'minimal':
        settings.durationMultiplier *= 0.3;
        settings.staggerMultiplier *= 0.2;
        settings.complexAnimations = false;
        break;
      case 'subtle':
        settings.durationMultiplier *= 0.7;
        settings.staggerMultiplier *= 0.7;
        break;
      case 'expressive':
        settings.durationMultiplier *= 1.2;
        settings.staggerMultiplier *= 1.3;
        break;
    }
    
    // Apply context mode settings
    switch (this.preferences.contextMode) {
      case 'field':
        settings.durationMultiplier *= 0.8;
        settings.staggerMultiplier *= 0.6;
        settings.complexAnimations = false;
        break;
      case 'focus':
        settings.durationMultiplier *= 0.5;
        settings.staggerMultiplier *= 0.3;
        settings.complexAnimations = false;
        break;
      case 'presentation':
        settings.durationMultiplier *= 1.3;
        settings.staggerMultiplier *= 1.2;
        break;
    }
    
    // Apply device capability adjustments
    if (!this.deviceCapabilities.highPerformance) {
      settings.durationMultiplier *= 0.8;
      settings.complexAnimations = false;
    }
    
    if (this.deviceCapabilities.lowBandwidth) {
      settings.durationMultiplier *= 0.7;
      settings.complexAnimations = false;
    }
    
    return settings;
  }
  
  // Utility function for debouncing
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// Adaptive Animation System
class AdaptiveAnimationSystem {
  constructor() {
    this.preferenceManager = new AnimationPreferenceManager();
    this.setupEventListeners();
    this.initializeAnimations();
  }
  
  setupEventListeners() {
    // Listen for preference changes
    window.addEventListener('animationpreferenceschanged', () => {
      this.updateAnimationSettings();
    });
    
    // Listen for screen reader activity
    document.addEventListener('focusin', (e) => {
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT' || e.target.tagName === 'TEXTAREA') {
        this.pauseNonEssentialAnimations();
      }
    });
  }
  
  updateAnimationSettings() {
    this.settings = this.preferenceManager.getAnimationSettings();
    
    // Update GSAP defaults
    gsap.defaults({
      duration: 0.5 * this.settings.durationMultiplier,
      ease: "power2.out"
    });
    
    // Apply settings to running animations
    if (!this.settings.enabled) {
      this.pauseAllAnimations();
    } else {
      this.resumeAllAnimations();
    }
  }
  
  pauseAllAnimations() {
    gsap.globalTimeline.pause();
  }
  
  resumeAllAnimations() {
    gsap.globalTimeline.play();
  }
  
  pauseNonEssentialAnimations() {
    // Find and pause non-essential animations
    gsap.globalTimeline.getChildren().forEach(tween => {
      if (tween.vars && !tween.vars.essential) {
        tween.pause();
      }
    });
  }
  
  resumeNonEssentialAnimations() {
    // Find and resume non-essential animations
    gsap.globalTimeline.getChildren().forEach(tween => {
      if (tween.vars && !tween.vars.essential && !tween.vars.paused) {
        tween.play();
      }
    });
  }
  
  // Create an adaptive animation
  createAnimation(target, properties, options = {}) {
    // Skip if animations disabled
    if (!this.settings.enabled) return null;
    
    // Apply adaptive settings
    const adaptedOptions = { ...options };
    
    // Adjust duration
    if (adaptedOptions.duration) {
      adaptedOptions.duration *= this.settings.durationMultiplier;
    }
    
    // Adjust stagger
    if (adaptedOptions.stagger) {
      if (typeof adaptedOptions.stagger === 'number') {
        adaptedOptions.stagger *= this.settings.staggerMultiplier;
      } else {
        adaptedOptions.stagger.amount *= this.settings.staggerMultiplier;
      }
    }
    
    // Handle reduced motion
    if (this.settings.reducedMotion) {
      // Provide alternative animation for reduced motion
      if (options.reducedMotionAlternative) {
        return options.reducedMotionAlternative(target, this.settings);
      }
      
      // Simplify animation by removing motion properties
      const simplifiedProps = { ...properties };
      
      // Remove or reduce motion properties
      if ('x' in simplifiedProps) simplifiedProps.x = 0;
      if ('y' in simplifiedProps) simplifiedProps.y = 0;
      if ('rotation' in simplifiedProps) simplifiedProps.rotation = 0;
      if ('rotationX' in simplifiedProps) simplifiedProps.rotationX = 0;
      if ('rotationY' in simplifiedProps) simplifiedProps.rotationY = 0;
      
      // Keep opacity for visibility changes
      
      return gsap.to(target, {
        ...simplifiedProps,
        ...adaptedOptions
      });
    }
    
    // Skip complex animations if not supported
    if (!this.settings.complexAnimations && options.isComplex) {
      // Provide simplified alternative
      if (options.simplifiedAlternative) {
        return options.simplifiedAlternative(target, this.settings);
      }
      
      // Default simplified animation
      return gsap.to(target, {
        opacity: properties.opacity || 1,
        duration: adaptedOptions.duration || 0.3
      });
    }
    
    // Create the animation with adapted options
    return gsap.to(target, {
      ...properties,
      ...adaptedOptions
    });
  }
  
  // Initialize animations based on current settings
  initializeAnimations() {
    this.settings = this.preferenceManager.getAnimationSettings();
    
    // Set up UI for animation preferences
    this.setupPreferenceUI();
    
    // Initialize page animations
    this.initPageAnimations();
  }
  
  // Set up UI for changing animation preferences
  setupPreferenceUI() {
    const preferencesForm = document.querySelector('#animation-preferences-form');
    if (!preferencesForm) return;
    
    // Set initial values
    const intensitySelect = preferencesForm.querySelector('[name="motion-intensity"]');
    const reducedMotionToggle = preferencesForm.querySelector('[name="reduced-motion"]');
    const animationsEnabledToggle = preferencesForm.querySelector('[name="animations-enabled"]');
    const contextModeSelect = preferencesForm.querySelector('[name="context-mode"]');
    
    if (intensitySelect) {
      intensitySelect.value = this.preferenceManager.preferences.motionIntensity;
    }
    
    if (reducedMotionToggle) {
      reducedMotionToggle.checked = this.preferenceManager.preferences.reducedMotion;
    }
    
    if (animationsEnabledToggle) {
      animationsEnabledToggle.checked = this.preferenceManager.preferences.animationsEnabled;
    }
    
    if (contextModeSelect) {
      contextModeSelect.value = this.preferenceManager.preferences.contextMode;
    }
    
    // Set up event listeners
    preferencesForm.addEventListener('change', (e) => {
      const target = e.target;
      
      if (target.name === 'motion-intensity') {
        this.preferenceManager.setPreference('motionIntensity', target.value);
      } else if (target.name === 'reduced-motion') {
        this.preferenceManager.setPreference('reducedMotion', target.checked);
      } else if (target.name === 'animations-enabled') {
        this.preferenceManager.setPreference('animationsEnabled', target.checked);
      } else if (target.name === 'context-mode') {
        this.preferenceManager.setPreference('contextMode', target.value);
      }
    });
  }
  
  // Initialize page animations based on current settings
  initPageAnimations() {
    // Only proceed if animations are enabled
    if (!this.settings.enabled) return;
    
    // Example: Animate page header
    const header = document.querySelector('.page-header');
    if (header) {
      this.createAnimation(header, {
        y: 0,
        opacity: 1
      }, {
        duration: 0.7,
        from: { y: -30, opacity: 0 }
      });
    }
    
    // Example: Animate dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    if (dashboardCards.length) {
      this.createAnimation(dashboardCards, {
        y: 0,
        opacity: 1,
        scale: 1
      }, {
        duration: 0.5,
        stagger: 0.1,
        from: { y: 50, opacity: 0, scale: 0.9 },
        isComplex: true,
        simplifiedAlternative: (target) => {
          return gsap.to(target, {
            opacity: 1,
            duration: 0.3 * this.settings.durationMultiplier,
            stagger: 0.05 * this.settings.staggerMultiplier
          });
        }
      });
    }
  }
}

// Initialize the adaptive animation system
document.addEventListener('DOMContentLoaded', () => {
  window.animationSystem = new AdaptiveAnimationSystem();
});
```

## Integration with HVAC CRM

This approach supports the "Akceptacja Zmienności" principle from Profound.md by creating interfaces that adapt to changing user needs and contexts. Adaptive animation systems enable:

1. Field technicians to use the system effectively in challenging outdoor environments
2. Office staff to enjoy rich animations on high-performance devices
3. Users with motion sensitivity to use the system comfortably
4. The interface to remain responsive across a wide range of devices and network conditions
5. Animations that enhance rather than hinder accessibility

## Measurement of Success

- Improved system usability across diverse environments
- Reduced motion sickness complaints
- Enhanced accessibility compliance
- Improved performance on low-end devices
- Higher user satisfaction across different user groups
- Reduced battery consumption for mobile users