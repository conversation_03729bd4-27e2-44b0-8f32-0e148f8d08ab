# Reactive Animations for Real-Time Data

## Core Concept
Create animation systems that automatically respond to real-time data changes, providing immediate visual feedback and helping users understand dynamic information in the HVAC CRM system.

## Inspiration from Anime.js
Anime.js offers powerful animation control with dynamic parameter updates and function-based values. These techniques can be adapted to GSAP to create animations that react instantly to data changes from various sources.

## Implementation Guidelines

### 1. Data-Driven Animation Parameters
- Implement animation parameters that derive directly from data values
- Create scaling functions that map data ranges to animation properties
- Design animations that reflect data relationships through visual properties
- Implement threshold-based animation triggers for critical values

### 2. Real-Time Data Visualization
- Create continuously updating visualizations for sensor data
- Implement smooth transitions for changing values
- Design animations that show both current state and recent history
- Use animation speed and intensity to convey data urgency or importance

### 3. Event-Triggered Animations
- Design animation responses for system events (new orders, status changes)
- Create attention-drawing animations for critical alerts
- Implement subtle background animations for ongoing processes
- Design cascading animations for related data updates

### 4. Adaptive Animation Complexity
- Scale animation complexity based on data update frequency
- Implement animation throttling for high-frequency data sources
- Design animations that remain meaningful even with simplified parameters
- Create fallback visualizations for extreme data conditions

## Technical Implementation

```javascript
// Example of implementing reactive animations with GSAP
// Inspired by Anime.js approach to function-based values

// Create a reactive animation system for temperature data
function createTemperatureMonitor(sensorId, elementSelector) {
  const element = document.querySelector(elementSelector);
  let currentTemp = 0;
  let targetTemp = 0;
  let normalRange = [68, 76]; // Fahrenheit
  let animation;
  
  // Initialize the animation with placeholder value
  animation = gsap.to(element, {
    backgroundColor: getColorForTemperature(currentTemp),
    scale: getScaleForTemperature(currentTemp),
    duration: 1,
    paused: true
  });
  
  // Function to update the animation based on new temperature
  function updateTemperature(newTemp) {
    // Store the new target temperature
    targetTemp = newTemp;
    
    // Update the animation
    gsap.to(animation.vars, {
      backgroundColor: getColorForTemperature(newTemp),
      scale: getScaleForTemperature(newTemp),
      duration: 0.5,
      onUpdate: () => {
        // Apply the updated values
        animation.invalidate().restart();
      }
    });
    
    // Update the text display
    gsap.to(element.querySelector('.temperature-value'), {
      innerText: Math.round(newTemp),
      duration: 0.5,
      snap: { innerText: 1 }
    });
    
    // Add alert animation if temperature is outside normal range
    if (newTemp < normalRange[0] || newTemp > normalRange[1]) {
      // Create attention-grabbing pulse
      gsap.to(element, {
        boxShadow: `0 0 20px ${newTemp > normalRange[1] ? 'rgba(255,0,0,0.6)' : 'rgba(0,0,255,0.6)'}`,
        repeat: 3,
        yoyo: true,
        duration: 0.3
      });
    }
  }
  
  // Helper function to map temperature to color
  function getColorForTemperature(temp) {
    if (temp < normalRange[0] - 5) return "#0022ff"; // Very cold
    if (temp < normalRange[0]) return "#00aaff";     // Cold
    if (temp <= normalRange[1]) return "#00ff00";    // Normal
    if (temp <= normalRange[1] + 5) return "#ffaa00"; // Warm
    return "#ff0000";                                // Hot
  }
  
  // Helper function to map temperature to scale
  function getScaleForTemperature(temp) {
    // Calculate how far the temperature is from the normal range
    const normalMid = (normalRange[0] + normalRange[1]) / 2;
    const deviation = Math.abs(temp - normalMid) / 20; // Normalize
    
    // Scale between 1 and 1.2 based on deviation
    return 1 + Math.min(deviation, 0.2);
  }
  
  // Set up WebSocket or polling for real-time updates
  function connectToDataSource() {
    // Example using WebSocket
    const socket = new WebSocket(`wss://hvac-api.example.com/sensors/${sensorId}`);
    
    socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.temperature !== undefined) {
        updateTemperature(data.temperature);
      }
    };
    
    socket.onerror = () => {
      // Fallback to polling on WebSocket error
      startPolling();
    };
    
    // Cleanup function
    return () => socket.close();
  }
  
  // Fallback polling function
  function startPolling() {
    const pollInterval = setInterval(() => {
      fetch(`https://hvac-api.example.com/sensors/${sensorId}`)
        .then(response => response.json())
        .then(data => {
          if (data.temperature !== undefined) {
            updateTemperature(data.temperature);
          }
        })
        .catch(error => console.error('Polling error:', error));
    }, 5000); // Poll every 5 seconds
    
    // Cleanup function
    return () => clearInterval(pollInterval);
  }
  
  // Start data connection
  const cleanup = connectToDataSource();
  
  // Return control object
  return {
    setNormalRange: (min, max) => {
      normalRange = [min, max];
      // Update animation with new normal range
      updateTemperature(targetTemp);
    },
    getCurrentTemp: () => currentTemp,
    disconnect: cleanup
  };
}

// Create a reactive animation for service order status
function createServiceOrderStatusMonitor(orderId, elementSelector) {
  const element = document.querySelector(elementSelector);
  const statusElement = element.querySelector('.status-indicator');
  const progressElement = element.querySelector('.progress-bar');
  
  // Status definitions with associated animations
  const statusConfigs = {
    'scheduled': {
      color: '#1890ff',
      icon: 'calendar',
      animation: () => {
        return gsap.to(statusElement, {
          backgroundColor: '#1890ff',
          ease: 'power2.out',
          duration: 0.5
        });
      }
    },
    'in-progress': {
      color: '#fa8c16',
      icon: 'tool',
      animation: () => {
        // Create pulsing animation
        const timeline = gsap.timeline({repeat: -1, yoyo: true});
        timeline.to(statusElement, {
          backgroundColor: '#fa8c16',
          boxShadow: '0 0 10px rgba(250, 140, 22, 0.7)',
          duration: 1
        });
        return timeline;
      }
    },
    'completed': {
      color: '#52c41a',
      icon: 'check',
      animation: () => {
        // Create completion animation
        const timeline = gsap.timeline();
        timeline.to(statusElement, {
          backgroundColor: '#52c41a',
          scale: 1.2,
          duration: 0.3
        }).to(statusElement, {
          scale: 1,
          duration: 0.2
        });
        return timeline;
      }
    },
    'delayed': {
      color: '#faad14',
      icon: 'clock',
      animation: () => {
        // Create attention animation
        const timeline = gsap.timeline({repeat: 2});
        timeline.to(statusElement, {
          backgroundColor: '#faad14',
          x: -5,
          duration: 0.1
        }).to(statusElement, {
          x: 5,
          duration: 0.1
        }).to(statusElement, {
          x: 0,
          duration: 0.1
        });
        return timeline;
      }
    },
    'cancelled': {
      color: '#ff4d4f',
      icon: 'close',
      animation: () => {
        // Create cancellation animation
        const timeline = gsap.timeline();
        timeline.to(statusElement, {
          backgroundColor: '#ff4d4f',
          rotation: 180,
          duration: 0.5
        });
        return timeline;
      }
    }
  };
  
  let currentStatus = '';
  let currentAnimation = null;
  
  // Function to update status
  function updateStatus(newStatus, progress = 0) {
    // Stop current animation if it exists
    if (currentAnimation) {
      currentAnimation.kill();
    }
    
    // Get the configuration for the new status
    const config = statusConfigs[newStatus] || statusConfigs['scheduled'];
    
    // Update the icon
    statusElement.querySelector('i').className = `icon icon-${config.icon}`;
    
    // Start the new animation
    currentAnimation = config.animation();
    
    // Update the progress bar
    gsap.to(progressElement, {
      width: `${progress}%`,
      backgroundColor: config.color,
      duration: 0.5
    });
    
    // Update text status
    gsap.to(element.querySelector('.status-text'), {
      innerText: newStatus.replace('-', ' '),
      color: config.color,
      duration: 0.3
    });
    
    // Store the current status
    currentStatus = newStatus;
  }
  
  // Set up event source for status updates
  const eventSource = new EventSource(`https://hvac-api.example.com/orders/${orderId}/status-stream`);
  
  eventSource.addEventListener('status-update', (event) => {
    const data = JSON.parse(event.data);
    updateStatus(data.status, data.progress);
  });
  
  // Initial status fetch
  fetch(`https://hvac-api.example.com/orders/${orderId}`)
    .then(response => response.json())
    .then(data => {
      updateStatus(data.status, data.progress);
    });
  
  // Return control object
  return {
    getCurrentStatus: () => currentStatus,
    disconnect: () => eventSource.close()
  };
}
```

## Integration with HVAC CRM

This approach supports the "Non-Locality" principle from Profound.md by creating visual connections between related data points across the system. Reactive animations enable:

1. Technicians to monitor equipment status with intuitive visual feedback
2. Managers to track service order progress through animated status indicators
3. Customer service representatives to see real-time updates on customer interactions
4. System administrators to monitor system health through animated dashboards

## Measurement of Success

- Improved user awareness of changing data
- Reduced time to identify critical status changes
- Enhanced understanding of data relationships
- More engaging and informative dashboards
- Reduced need for manual data refreshing