version: '3.8'

services:
  # Agent Protocol Server
  agent-protocol-server:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./server:/app
      - agent_protocol_data:/app/data
    networks:
      - agent-protocol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - agent-protocol-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # PostgreSQL for persistent storage
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: agent_protocol
      POSTGRES_USER: agent_protocol
      POSTGRES_PASSWORD: agent_protocol_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - agent-protocol-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent_protocol"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database for embeddings
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - agent-protocol-network
    restart: unless-stopped
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - agent-protocol-server
    networks:
      - agent-protocol-network
    restart: unless-stopped

volumes:
  agent_protocol_data:
  redis_data:
  postgres_data:
  qdrant_data:

networks:
  agent-protocol-network:
    driver: bridge
