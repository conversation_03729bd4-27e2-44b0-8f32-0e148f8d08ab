[project]
name = "ap-tooling"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON><PERSON>",email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "../README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "fastapi-code-generator (>=0.5.2,<0.6.0)",
    "autoflake (>=2.3.1,<3.0.0)",
    "ruff (>=0.9.4,<0.10.0)"
]


[tool.poetry]
package-mode = false


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
