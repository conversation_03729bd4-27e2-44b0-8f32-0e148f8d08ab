.github/workflows/python.yml
.gitignore
.gitlab-ci.yml
.travis.yml
README.md
ap_client/__init__.py
ap_client/api/__init__.py
ap_client/api/agents_api.py
ap_client/api/background_runs_api.py
ap_client/api/runs_api.py
ap_client/api/store_api.py
ap_client/api/threads_api.py
ap_client/api_client.py
ap_client/api_response.py
ap_client/configuration.py
ap_client/exceptions.py
ap_client/models/__init__.py
ap_client/models/agent.py
ap_client/models/agent_capabilities.py
ap_client/models/agent_schema.py
ap_client/models/config.py
ap_client/models/content.py
ap_client/models/content_one_of_inner.py
ap_client/models/error_response.py
ap_client/models/input.py
ap_client/models/item.py
ap_client/models/message.py
ap_client/models/message_any_block.py
ap_client/models/message_text_block.py
ap_client/models/run.py
ap_client/models/run_create.py
ap_client/models/run_search_request.py
ap_client/models/run_status.py
ap_client/models/run_stream.py
ap_client/models/run_wait_response.py
ap_client/models/search_agents_request.py
ap_client/models/search_items_response.py
ap_client/models/store_delete_request.py
ap_client/models/store_list_namespaces_request.py
ap_client/models/store_put_request.py
ap_client/models/store_search_request.py
ap_client/models/stream_mode.py
ap_client/models/thread.py
ap_client/models/thread_checkpoint.py
ap_client/models/thread_create.py
ap_client/models/thread_patch.py
ap_client/models/thread_search_request.py
ap_client/models/thread_state.py
ap_client/models/thread_status.py
ap_client/py.typed
ap_client/rest.py
docs/Agent.md
docs/AgentCapabilities.md
docs/AgentSchema.md
docs/AgentsApi.md
docs/BackgroundRunsApi.md
docs/Config.md
docs/Content.md
docs/ContentOneOfInner.md
docs/ErrorResponse.md
docs/Input.md
docs/Item.md
docs/Message.md
docs/MessageAnyBlock.md
docs/MessageTextBlock.md
docs/Run.md
docs/RunCreate.md
docs/RunSearchRequest.md
docs/RunStatus.md
docs/RunStream.md
docs/RunWaitResponse.md
docs/RunsApi.md
docs/SearchAgentsRequest.md
docs/SearchItemsResponse.md
docs/StoreApi.md
docs/StoreDeleteRequest.md
docs/StoreListNamespacesRequest.md
docs/StorePutRequest.md
docs/StoreSearchRequest.md
docs/StreamMode.md
docs/Thread.md
docs/ThreadCheckpoint.md
docs/ThreadCreate.md
docs/ThreadPatch.md
docs/ThreadSearchRequest.md
docs/ThreadState.md
docs/ThreadStatus.md
docs/ThreadsApi.md
git_push.sh
pyproject.toml
requirements.txt
setup.cfg
setup.py
test-requirements.txt
test/__init__.py
test/test_agent_schema.py
test/test_run_stream.py
tox.ini
