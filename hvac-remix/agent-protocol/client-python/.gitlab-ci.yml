# NOTE: This file is auto generated by OpenAPI Generator.
# URL: https://openapi-generator.tech
#
# ref: https://docs.gitlab.com/ee/ci/README.html
# ref: https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Python.gitlab-ci.yml

stages:
  - test

.pytest:
  stage: test
  script:
   - pip install -r requirements.txt
   - pip install -r test-requirements.txt
   - pytest --cov=ap_client

pytest-3.9:
  extends: .pytest
  image: python:3.9-alpine
pytest-3.10:
  extends: .pytest
  image: python:3.10-alpine
pytest-3.11:
  extends: .pytest
  image: python:3.11-alpine
pytest-3.12:
  extends: .pytest
  image: python:3.12-alpine
pytest-3.13:
  extends: .pytest
  image: python:3.13-alpine
