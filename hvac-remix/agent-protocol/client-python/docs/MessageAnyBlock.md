# MessageAnyBlock


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**type** | **str** |  | 
**metadata** | **object** |  | [optional] 

## Example

```python
from ap_client.models.message_any_block import Message<PERSON><PERSON><PERSON><PERSON>

# TODO update the JSON string below
json = "{}"
# create an instance of MessageAny<PERSON>lock from a JSON string
message_any_block_instance = MessageAnyBlock.from_json(json)
# print the JSON string representation of the object
print(MessageAnyBlock.to_json())

# convert the object into a dict
message_any_block_dict = message_any_block_instance.to_dict()
# create an instance of MessageAny<PERSON>lock from a dict
message_any_block_from_dict = MessageAnyBlock.from_dict(message_any_block_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


