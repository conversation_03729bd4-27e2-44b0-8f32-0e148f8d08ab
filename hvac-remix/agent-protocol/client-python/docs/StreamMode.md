# StreamMode

The stream mode(s) to use.

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------

## Example

```python
from ap_client.models.stream_mode import StreamMode

# TODO update the JSON string below
json = "{}"
# create an instance of StreamMode from a JSON string
stream_mode_instance = StreamMode.from_json(json)
# print the JSON string representation of the object
print(StreamMode.to_json())

# convert the object into a dict
stream_mode_dict = stream_mode_instance.to_dict()
# create an instance of StreamMode from a dict
stream_mode_from_dict = StreamMode.from_dict(stream_mode_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


