# ContentOneOfInner


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**text** | **str** |  | 
**type** | **str** |  | 
**metadata** | **object** |  | [optional] 

## Example

```python
from ap_client.models.content_one_of_inner import ContentOneOfInner

# TODO update the JSON string below
json = "{}"
# create an instance of ContentOneOfInner from a JSON string
content_one_of_inner_instance = ContentOneOfInner.from_json(json)
# print the JSON string representation of the object
print(ContentOneOfInner.to_json())

# convert the object into a dict
content_one_of_inner_dict = content_one_of_inner_instance.to_dict()
# create an instance of ContentOneOfInner from a dict
content_one_of_inner_from_dict = ContentOneOfInner.from_dict(content_one_of_inner_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


