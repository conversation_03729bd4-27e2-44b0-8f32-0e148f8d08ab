# RunStatus

The status of the run. One of 'pending', 'error', 'success', 'timeout', 'interrupted'.

## Enum

* `PENDING` (value: `'pending'`)

* `ERROR` (value: `'error'`)

* `SUCCESS` (value: `'success'`)

* `TIMEOUT` (value: `'timeout'`)

* `INTERRUPTED` (value: `'interrupted'`)

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


