# coding: utf-8

"""
Agent Protocol

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

The version of the OpenAPI document: 0.1.6
Generated by OpenAPI Generator (https://openapi-generator.tech)

Do not edit the class manually.
"""  # noqa: E501

from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Set
from typing_extensions import Self


class AgentSchema(BaseModel):
    """
    Defines the structure and properties of an agent.
    """  # noqa: E501

    agent_id: StrictStr = Field(description="The ID of the agent.")
    input_schema: Dict[str, Any] = Field(
        description="The schema for the agent input. In JSON Schema format."
    )
    output_schema: Dict[str, Any] = Field(
        description="The schema for the agent output. In JSON Schema format."
    )
    state_schema: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The schema for the agent's internal state. In JSON Schema format.",
    )
    config_schema: Optional[Dict[str, Any]] = Field(
        default=None,
        description="The schema for the agent config. In JSON Schema format.",
    )
    __properties: ClassVar[List[str]] = [
        "agent_id",
        "input_schema",
        "output_schema",
        "state_schema",
        "config_schema",
    ]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of AgentSchema from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of AgentSchema from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate(
            {
                "agent_id": obj.get("agent_id"),
                "input_schema": obj.get("input_schema"),
                "output_schema": obj.get("output_schema"),
                "state_schema": obj.get("state_schema"),
                "config_schema": obj.get("config_schema"),
            }
        )
        return _obj
