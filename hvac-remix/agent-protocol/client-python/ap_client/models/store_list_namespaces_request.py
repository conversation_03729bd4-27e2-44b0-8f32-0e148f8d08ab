# coding: utf-8

"""
Agent Protocol

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

The version of the OpenAPI document: 0.1.6
Generated by OpenAPI Generator (https://openapi-generator.tech)

Do not edit the class manually.
"""  # noqa: E501

from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Set
from typing_extensions import Self


class StoreListNamespacesRequest(BaseModel):
    """
    StoreListNamespacesRequest
    """  # noqa: E501

    prefix: Optional[List[StrictStr]] = Field(
        default=None,
        description="Optional list of strings representing the prefix to filter namespaces.",
    )
    suffix: Optional[List[StrictStr]] = Field(
        default=None,
        description="Optional list of strings representing the suffix to filter namespaces.",
    )
    max_depth: Optional[StrictInt] = Field(
        default=None,
        description="Optional integer specifying the maximum depth of namespaces to return.",
    )
    limit: Optional[StrictInt] = Field(
        default=100,
        description="Maximum number of namespaces to return (default is 100).",
    )
    offset: Optional[StrictInt] = Field(
        default=0,
        description="Number of namespaces to skip before returning results (default is 0).",
    )
    __properties: ClassVar[List[str]] = [
        "prefix",
        "suffix",
        "max_depth",
        "limit",
        "offset",
    ]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of StoreListNamespacesRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of StoreListNamespacesRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate(
            {
                "prefix": obj.get("prefix"),
                "suffix": obj.get("suffix"),
                "max_depth": obj.get("max_depth"),
                "limit": obj.get("limit") if obj.get("limit") is not None else 100,
                "offset": obj.get("offset") if obj.get("offset") is not None else 0,
            }
        )
        return _obj
