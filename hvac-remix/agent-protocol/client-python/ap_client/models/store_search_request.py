# coding: utf-8

"""
Agent Protocol

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

The version of the OpenAPI document: 0.1.6
Generated by OpenAPI Generator (https://openapi-generator.tech)

Do not edit the class manually.
"""  # noqa: E501

from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Set
from typing_extensions import Self


class StoreSearchRequest(BaseModel):
    """
    Request to search for items
    """  # noqa: E501

    namespace_prefix: Optional[List[StrictStr]] = Field(
        default=None, description="List of strings representing the namespace prefix."
    )
    filter: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Optional dictionary of key-value pairs to filter results.",
    )
    limit: Optional[StrictInt] = Field(
        default=10, description="Maximum number of items to return (default is 10)."
    )
    offset: Optional[StrictInt] = Field(
        default=0,
        description="Number of items to skip before returning results (default is 0).",
    )
    __properties: ClassVar[List[str]] = [
        "namespace_prefix",
        "filter",
        "limit",
        "offset",
    ]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of StoreSearchRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # set to None if namespace_prefix (nullable) is None
        # and model_fields_set contains the field
        if (
            self.namespace_prefix is None
            and "namespace_prefix" in self.model_fields_set
        ):
            _dict["namespace_prefix"] = None

        # set to None if filter (nullable) is None
        # and model_fields_set contains the field
        if self.filter is None and "filter" in self.model_fields_set:
            _dict["filter"] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of StoreSearchRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate(
            {
                "namespace_prefix": obj.get("namespace_prefix"),
                "filter": obj.get("filter"),
                "limit": obj.get("limit") if obj.get("limit") is not None else 10,
                "offset": obj.get("offset") if obj.get("offset") is not None else 0,
            }
        )
        return _obj
