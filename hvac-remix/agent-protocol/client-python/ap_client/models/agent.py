# coding: utf-8

"""
Agent Protocol

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

The version of the OpenAPI document: 0.1.6
Generated by OpenAPI Generator (https://openapi-generator.tech)

Do not edit the class manually.
"""  # noqa: E501

from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from ap_client.models.agent_capabilities import AgentCapabilities
from typing import Set
from typing_extensions import Self


class Agent(BaseModel):
    """
    Agent
    """  # noqa: E501

    agent_id: StrictStr = Field(description="The ID of the agent.")
    name: StrictStr = Field(description="The name of the agent")
    description: Optional[StrictStr] = Field(
        default=None, description="The description of the agent."
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="The agent metadata."
    )
    capabilities: AgentCapabilities
    __properties: ClassVar[List[str]] = [
        "agent_id",
        "name",
        "description",
        "metadata",
        "capabilities",
    ]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of Agent from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of capabilities
        if self.capabilities:
            _dict["capabilities"] = self.capabilities.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of Agent from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate(
            {
                "agent_id": obj.get("agent_id"),
                "name": obj.get("name"),
                "description": obj.get("description"),
                "metadata": obj.get("metadata"),
                "capabilities": AgentCapabilities.from_dict(obj["capabilities"])
                if obj.get("capabilities") is not None
                else None,
            }
        )
        return _obj
