# coding: utf-8

"""
Agent Protocol

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

The version of the OpenAPI document: 0.1.6
Generated by OpenAPI Generator (https://openapi-generator.tech)

Do not edit the class manually.
"""  # noqa: E501

from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from ap_client.models.message import Message
from ap_client.models.thread_checkpoint import ThreadCheckpoint
from typing import Set
from typing_extensions import Self


class ThreadState(BaseModel):
    """
    ThreadState
    """  # noqa: E501

    checkpoint: ThreadCheckpoint = Field(
        description="The identifier for this checkpoint."
    )
    values: Dict[str, Any] = Field(description="The current state of the thread.")
    messages: Optional[List[Message]] = Field(
        default=None,
        description="The current messages of the thread. This key isn't present for agents that don't support messages.",
    )
    metadata: Optional[Dict[str, Any]] = Field(
        default=None, description="The checkpoint metadata."
    )
    __properties: ClassVar[List[str]] = ["checkpoint", "values", "messages", "metadata"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ThreadState from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of checkpoint
        if self.checkpoint:
            _dict["checkpoint"] = self.checkpoint.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in messages (list)
        _items = []
        if self.messages:
            for _item_messages in self.messages:
                if _item_messages:
                    _items.append(_item_messages.to_dict())
            _dict["messages"] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ThreadState from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate(
            {
                "checkpoint": ThreadCheckpoint.from_dict(obj["checkpoint"])
                if obj.get("checkpoint") is not None
                else None,
                "values": obj.get("values"),
                "messages": [Message.from_dict(_item) for _item in obj["messages"]]
                if obj.get("messages") is not None
                else None,
                "metadata": obj.get("metadata"),
            }
        )
        return _obj
