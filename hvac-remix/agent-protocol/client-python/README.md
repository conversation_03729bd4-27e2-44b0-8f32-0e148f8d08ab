# ap-client
No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: 0.1.6
- Package version: 1.0.0
- Generator version: 7.13.0-SNAPSHOT
- Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.9+

## Installation & Usage
### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import ap_client
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import ap_client
```

### Tests

Execute `pytest` to run the tests.

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python

import ap_client
from ap_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost
# See configuration.py for a list of all supported configuration parameters.
configuration = ap_client.Configuration(
    host = "http://localhost"
)



# Enter a context with an instance of the API client
with ap_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = ap_client.AgentsApi(api_client)
    agent_id = 'agent_id_example' # str | The ID of the agent.

    try:
        # Get Agent
        api_response = api_instance.get_agent(agent_id)
        print("The response of AgentsApi->get_agent:\n")
        pprint(api_response)
    except ApiException as e:
        print("Exception when calling AgentsApi->get_agent: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*AgentsApi* | [**get_agent**](docs/AgentsApi.md#get_agent) | **GET** /agents/{agent_id} | Get Agent
*AgentsApi* | [**get_agent_schemas**](docs/AgentsApi.md#get_agent_schemas) | **GET** /agents/{agent_id}/schemas | Get Agent Schemas
*AgentsApi* | [**search_agents**](docs/AgentsApi.md#search_agents) | **POST** /agents/search | Search Agents
*BackgroundRunsApi* | [**cancel_run**](docs/BackgroundRunsApi.md#cancel_run) | **POST** /runs/{run_id}/cancel | Cancel Run
*BackgroundRunsApi* | [**create_run**](docs/BackgroundRunsApi.md#create_run) | **POST** /runs | Create Background Run
*BackgroundRunsApi* | [**delete_run**](docs/BackgroundRunsApi.md#delete_run) | **DELETE** /runs/{run_id} | Delete Run
*BackgroundRunsApi* | [**get_run**](docs/BackgroundRunsApi.md#get_run) | **GET** /runs/{run_id} | Get Run
*BackgroundRunsApi* | [**search_runs**](docs/BackgroundRunsApi.md#search_runs) | **POST** /runs/search | Search Runs
*BackgroundRunsApi* | [**stream_run**](docs/BackgroundRunsApi.md#stream_run) | **GET** /runs/{run_id}/stream | Stream output from Run
*BackgroundRunsApi* | [**wait_run**](docs/BackgroundRunsApi.md#wait_run) | **GET** /runs/{run_id}/wait | Wait for Run output
*RunsApi* | [**create_and_stream_run**](docs/RunsApi.md#create_and_stream_run) | **POST** /runs/stream | Create Run, Stream Output
*RunsApi* | [**create_and_wait_run**](docs/RunsApi.md#create_and_wait_run) | **POST** /runs/wait | Create Run, Wait for Output
*StoreApi* | [**delete_item**](docs/StoreApi.md#delete_item) | **DELETE** /store/items | Delete Store Item
*StoreApi* | [**get_item**](docs/StoreApi.md#get_item) | **GET** /store/items | Get Store Item
*StoreApi* | [**list_namespaces**](docs/StoreApi.md#list_namespaces) | **POST** /store/namespaces | List namespaces
*StoreApi* | [**put_item**](docs/StoreApi.md#put_item) | **PUT** /store/items | Insert or Update Item
*StoreApi* | [**search_items**](docs/StoreApi.md#search_items) | **POST** /store/items/search | Search Store Items
*ThreadsApi* | [**copy_thread**](docs/ThreadsApi.md#copy_thread) | **POST** /threads/{thread_id}/copy | Copy Thread
*ThreadsApi* | [**create_thread**](docs/ThreadsApi.md#create_thread) | **POST** /threads | Create Thread
*ThreadsApi* | [**delete_thread**](docs/ThreadsApi.md#delete_thread) | **DELETE** /threads/{thread_id} | Delete Thread
*ThreadsApi* | [**get_thread**](docs/ThreadsApi.md#get_thread) | **GET** /threads/{thread_id} | Get Thread
*ThreadsApi* | [**get_thread_history**](docs/ThreadsApi.md#get_thread_history) | **GET** /threads/{thread_id}/history | Get Thread History
*ThreadsApi* | [**patch_thread**](docs/ThreadsApi.md#patch_thread) | **PATCH** /threads/{thread_id} | Patch Thread
*ThreadsApi* | [**search_threads**](docs/ThreadsApi.md#search_threads) | **POST** /threads/search | Search Threads


## Documentation For Models

 - [Agent](docs/Agent.md)
 - [AgentCapabilities](docs/AgentCapabilities.md)
 - [AgentSchema](docs/AgentSchema.md)
 - [Config](docs/Config.md)
 - [Content](docs/Content.md)
 - [ContentOneOfInner](docs/ContentOneOfInner.md)
 - [ErrorResponse](docs/ErrorResponse.md)
 - [Input](docs/Input.md)
 - [Item](docs/Item.md)
 - [Message](docs/Message.md)
 - [MessageAnyBlock](docs/MessageAnyBlock.md)
 - [MessageTextBlock](docs/MessageTextBlock.md)
 - [Run](docs/Run.md)
 - [RunCreate](docs/RunCreate.md)
 - [RunSearchRequest](docs/RunSearchRequest.md)
 - [RunStatus](docs/RunStatus.md)
 - [RunStream](docs/RunStream.md)
 - [RunWaitResponse](docs/RunWaitResponse.md)
 - [SearchAgentsRequest](docs/SearchAgentsRequest.md)
 - [SearchItemsResponse](docs/SearchItemsResponse.md)
 - [StoreDeleteRequest](docs/StoreDeleteRequest.md)
 - [StoreListNamespacesRequest](docs/StoreListNamespacesRequest.md)
 - [StorePutRequest](docs/StorePutRequest.md)
 - [StoreSearchRequest](docs/StoreSearchRequest.md)
 - [StreamMode](docs/StreamMode.md)
 - [Thread](docs/Thread.md)
 - [ThreadCheckpoint](docs/ThreadCheckpoint.md)
 - [ThreadCreate](docs/ThreadCreate.md)
 - [ThreadPatch](docs/ThreadPatch.md)
 - [ThreadSearchRequest](docs/ThreadSearchRequest.md)
 - [ThreadState](docs/ThreadState.md)
 - [ThreadStatus](docs/ThreadStatus.md)


<a id="documentation-for-authorization"></a>
## Documentation For Authorization

Endpoints do not require authorization.


## Author




