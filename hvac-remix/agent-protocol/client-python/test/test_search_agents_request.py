# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.models.search_agents_request import SearchAgentsRequest

class TestSearchAgentsRequest(unittest.TestCase):
    """SearchAgentsRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SearchAgentsRequest:
        """Test SearchAgentsRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SearchAgentsRequest`
        """
        model = SearchAgentsRequest()
        if include_optional:
            return SearchAgentsRequest(
                name = '',
                metadata = None,
                limit = 1,
                offset = 0
            )
        else:
            return SearchAgentsRequest(
        )
        """

    def testSearchAgentsRequest(self):
        """Test SearchAgentsRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
