# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.models.run import Run

class TestRun(unittest.TestCase):
    """Run unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> Run:
        """Test Run
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `Run`
        """
        model = Run()
        if include_optional:
            return Run(
                run_id = '',
                thread_id = '',
                agent_id = '',
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                updated_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                status = 'pending',
                metadata = ap_client.models.metadata.Metadata(),
                kwargs = ap_client.models.kwargs.Kwargs(),
                multitask_strategy = 'reject'
            )
        else:
            return Run(
                run_id = '',
                thread_id = '',
                created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                updated_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'),
                status = 'pending',
                metadata = ap_client.models.metadata.Metadata(),
                kwargs = ap_client.models.kwargs.Kwargs(),
                multitask_strategy = 'reject',
        )
        """

    def testRun(self):
        """Test Run"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
