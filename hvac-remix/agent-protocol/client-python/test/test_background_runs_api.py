# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.api.background_runs_api import BackgroundRunsApi


class TestBackgroundRunsApi(unittest.TestCase):
    """BackgroundRunsApi unit test stubs"""

    def setUp(self) -> None:
        self.api = BackgroundRunsApi()

    def tearDown(self) -> None:
        pass

    def test_cancel_run(self) -> None:
        """Test case for cancel_run

        Cancel Run
        """
        pass

    def test_create_run(self) -> None:
        """Test case for create_run

        Create Background Run
        """
        pass

    def test_delete_run(self) -> None:
        """Test case for delete_run

        Delete Run
        """
        pass

    def test_get_run(self) -> None:
        """Test case for get_run

        Get Run
        """
        pass

    def test_search_thread_runs(self) -> None:
        """Test case for search_thread_runs

        Search Thread Runs
        """
        pass

    def test_stream_run(self) -> None:
        """Test case for stream_run

        Stream output from Run
        """
        pass

    def test_wait_run(self) -> None:
        """Test case for wait_run

        Wait for Run output
        """
        pass


if __name__ == '__main__':
    unittest.main()
