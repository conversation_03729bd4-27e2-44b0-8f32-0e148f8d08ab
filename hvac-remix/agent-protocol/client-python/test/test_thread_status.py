# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.models.thread_status import ThreadStatus

class TestThreadStatus(unittest.TestCase):
    """ThreadStatus unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def testThreadStatus(self):
        """Test ThreadStatus"""
        # inst = ThreadStatus()

if __name__ == '__main__':
    unittest.main()
