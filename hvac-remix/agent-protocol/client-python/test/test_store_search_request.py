# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.models.store_search_request import StoreSearchRequest

class TestStoreSearchRequest(unittest.TestCase):
    """StoreSearchRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> StoreSearchRequest:
        """Test StoreSearchRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `StoreSearchRequest`
        """
        model = StoreSearchRequest()
        if include_optional:
            return StoreSearchRequest(
                namespace_prefix = [
                    ''
                    ],
                filter = { },
                limit = 56,
                offset = 56
            )
        else:
            return StoreSearchRequest(
        )
        """

    def testStoreSearchRequest(self):
        """Test StoreSearchRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
