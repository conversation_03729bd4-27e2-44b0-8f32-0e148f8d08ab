# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.models.run_stream import RunStream

class TestRunStream(unittest.TestCase):
    """RunStream unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> RunStream:
        """Test RunStream
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `RunStream`
        """
        model = RunStream()
        if include_optional:
            return RunStream(
                thread_id = '',
                agent_id = '',
                input = None,
                messages = [
                    {
                        'key' : null
                        }
                    ],
                metadata = ap_client.models.metadata.Metadata(),
                config = ap_client.models.config.Config(
                    tags = [
                        ''
                        ], 
                    recursion_limit = 56, 
                    configurable = ap_client.models.configurable.Configurable(), ),
                webhook = '0',
                on_completion = 'delete',
                on_disconnect = 'cancel',
                if_not_exists = 'reject',
                stream_mode = None
            )
        else:
            return RunStream(
        )
        """

    def testRunStream(self):
        """Test RunStream"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
