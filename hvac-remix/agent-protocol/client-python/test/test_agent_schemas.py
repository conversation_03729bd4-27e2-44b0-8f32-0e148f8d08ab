# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.models.agent_schemas import AgentSchemas

class TestAgentSchemas(unittest.TestCase):
    """AgentSchemas unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> AgentSchemas:
        """Test AgentSchemas
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `AgentSchemas`
        """
        model = AgentSchemas()
        if include_optional:
            return AgentSchemas(
                agent_id = '',
                input_schema = ap_client.models.input_schema.Input Schema(),
                output_schema = ap_client.models.output_schema.Output Schema(),
                state_schema = ap_client.models.state_schema.State Schema(),
                config_schema = ap_client.models.config_schema.Config Schema()
            )
        else:
            return AgentSchemas(
                agent_id = '',
                input_schema = ap_client.models.input_schema.Input Schema(),
                output_schema = ap_client.models.output_schema.Output Schema(),
        )
        """

    def testAgentSchemas(self):
        """Test AgentSchemas"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
