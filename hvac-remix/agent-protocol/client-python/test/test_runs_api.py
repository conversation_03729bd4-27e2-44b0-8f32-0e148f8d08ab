# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.api.runs_api import RunsApi


class TestRunsApi(unittest.TestCase):
    """RunsApi unit test stubs"""

    def setUp(self) -> None:
        self.api = RunsApi()

    def tearDown(self) -> None:
        pass

    def test_create_and_stream_run(self) -> None:
        """Test case for create_and_stream_run

        Create Run, Stream Output
        """
        pass

    def test_create_and_wait_run(self) -> None:
        """Test case for create_and_wait_run

        Create Run, Wait for Output
        """
        pass


if __name__ == '__main__':
    unittest.main()
