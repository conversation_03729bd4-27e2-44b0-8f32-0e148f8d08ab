# coding: utf-8

"""
    Agent Protocol

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: 0.1.6
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from ap_client.api.threads_api import ThreadsApi


class TestThreadsApi(unittest.TestCase):
    """ThreadsApi unit test stubs"""

    def setUp(self) -> None:
        self.api = ThreadsApi()

    def tearDown(self) -> None:
        pass

    def test_copy_thread(self) -> None:
        """Test case for copy_thread

        Copy Thread
        """
        pass

    def test_create_thread(self) -> None:
        """Test case for create_thread

        Create Thread
        """
        pass

    def test_delete_thread(self) -> None:
        """Test case for delete_thread

        Delete Thread
        """
        pass

    def test_get_thread(self) -> None:
        """Test case for get_thread

        Get Thread
        """
        pass

    def test_get_thread_history(self) -> None:
        """Test case for get_thread_history

        Get Thread History
        """
        pass

    def test_patch_thread(self) -> None:
        """Test case for patch_thread

        Patch Thread
        """
        pass

    def test_search_threads(self) -> None:
        """Test case for search_threads

        Search Threads
        """
        pass


if __name__ == '__main__':
    unittest.main()
