# generated by fastapi-codegen:
#   filename:  openapi.json

from __future__ import annotations

from fastapi import APIRouter

from ..models import ErrorResponse, RunCreate, RunStream, RunWaitResponse, Union

router = APIRouter(tags=["Runs"])


@router.post(
    "/runs/stream",
    response_model=str,
    responses={
        "404": {"model": ErrorResponse},
        "409": {"model": ErrorResponse},
        "422": {"model": ErrorResponse},
    },
    tags=["Runs"],
)
def create_and_stream_run(body: RunStream) -> Union[str, ErrorResponse]:
    """
    Create Run, Stream Output
    """
    pass


@router.post(
    "/runs/wait",
    response_model=RunWaitResponse,
    responses={
        "404": {"model": ErrorResponse},
        "409": {"model": ErrorResponse},
        "422": {"model": ErrorResponse},
    },
    tags=["Runs"],
)
def create_and_wait_run(body: RunCreate) -> Union[RunWaitResponse, ErrorResponse]:
    """
    Create Run, Wait for Output
    """
    pass
