# generated by fastapi-codegen:
#   filename:  openapi.json

from __future__ import annotations

from fastapi import APIRouter

from ..models import (
    ErrorResponse,
    Optional,
    Thread,
    ThreadCreate,
    ThreadPatch,
    ThreadSearchRequest,
    ThreadsSearchPostResponse,
    ThreadsThreadIdHistoryGetResponse,
    UUID,
    Union,
)

router = APIRouter(tags=["Threads"])


@router.post(
    "/threads",
    response_model=Thread,
    responses={"409": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def create_thread(body: ThreadCreate) -> Union[Thread, ErrorResponse]:
    """
    Create Thread
    """
    pass


@router.post(
    "/threads/search",
    response_model=ThreadsSearchPostResponse,
    responses={"422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def search_threads(
    body: ThreadSearchRequest,
) -> Union[ThreadsSearchPostResponse, ErrorResponse]:
    """
    Search Threads
    """
    pass


@router.get(
    "/threads/{thread_id}",
    response_model=Thread,
    responses={"404": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def get_thread(thread_id: UUID) -> Union[Thread, ErrorResponse]:
    """
    Get Thread
    """
    pass


@router.delete(
    "/threads/{thread_id}",
    response_model=None,
    status_code=204,
    responses={"404": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def delete_thread(thread_id: UUID) -> Optional[ErrorResponse]:
    """
    Delete Thread
    """
    pass


@router.patch(
    "/threads/{thread_id}",
    response_model=Thread,
    responses={"404": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def patch_thread(
    thread_id: UUID, body: ThreadPatch = ...
) -> Union[Thread, ErrorResponse]:
    """
    Patch Thread
    """
    pass


@router.post(
    "/threads/{thread_id}/copy",
    response_model=Thread,
    responses={"404": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def copy_thread(thread_id: UUID) -> Union[Thread, ErrorResponse]:
    """
    Copy Thread
    """
    pass


@router.get(
    "/threads/{thread_id}/history",
    response_model=ThreadsThreadIdHistoryGetResponse,
    responses={"404": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Threads"],
)
def get_thread_history(
    thread_id: UUID, limit: Optional[int] = 10, before: Optional[str] = None
) -> Union[ThreadsThreadIdHistoryGetResponse, ErrorResponse]:
    """
    Get Thread History
    """
    pass
