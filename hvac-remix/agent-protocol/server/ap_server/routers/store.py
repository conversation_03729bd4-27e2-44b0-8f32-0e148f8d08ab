# generated by fastapi-codegen:
#   filename:  openapi.json

from __future__ import annotations

from typing import Annotated

from fastapi import APIRouter, Query

from ..models import (
    ErrorResponse,
    Item,
    ListNamespaceResponse,
    Namespace,
    Optional,
    SearchItemsResponse,
    StoreDeleteRequest,
    StoreListNamespacesRequest,
    StorePutRequest,
    StoreSearchRequest,
    Union,
)

router = APIRouter(tags=["Store"])


@router.put(
    "/store/items",
    response_model=None,
    status_code=204,
    responses={"422": {"model": ErrorResponse}},
    tags=["Store"],
)
def put_item(body: StorePutRequest) -> Optional[ErrorResponse]:
    """
    Insert or Update Item
    """
    pass


@router.delete(
    "/store/items",
    response_model=None,
    status_code=204,
    responses={"404": {"model": ErrorResponse}, "422": {"model": ErrorResponse}},
    tags=["Store"],
)
def delete_item(body: StoreDeleteRequest) -> Optional[ErrorResponse]:
    """
    Delete Store Item
    """
    pass


@router.get(
    "/store/items",
    response_model=Item,
    responses={
        "400": {"model": ErrorResponse},
        "404": {"model": ErrorResponse},
        "422": {"model": ErrorResponse},
    },
    tags=["Store"],
)
def get_item(
    key: str, namespace: Annotated[list[str] | None, Query()] = None
) -> Union[Item, ErrorResponse]:
    """
    Get Store Item
    """
    pass


@router.post(
    "/store/items/search",
    response_model=SearchItemsResponse,
    responses={"422": {"model": ErrorResponse}},
    tags=["Store"],
)
def search_items(body: StoreSearchRequest) -> Union[SearchItemsResponse, ErrorResponse]:
    """
    Search Store Items
    """
    pass


@router.post(
    "/store/namespaces",
    response_model=ListNamespaceResponse,
    responses={"422": {"model": ErrorResponse}},
    tags=["Store"],
)
def list_namespaces(
    body: StoreListNamespacesRequest,
) -> Union[ListNamespaceResponse, ErrorResponse]:
    """
    List namespaces
    """
    pass
