"""
Store models for Agent Protocol.
"""

from typing import Dict, List, Optional, Any, Literal, Union
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID, uuid4


class StoreItem(BaseModel):
    """Store item model."""
    
    namespace: List[str]
    key: str
    value: Any
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class StoreItemCreate(BaseModel):
    """Store item creation model."""
    
    namespace: List[str]
    key: str
    value: Any


class StoreItemUpdate(BaseModel):
    """Store item update model."""
    
    value: Any


class StoreItemSearchParams(BaseModel):
    """Store item search parameters."""
    
    namespace: Optional[List[str]] = None
    key: Optional[str] = None
    limit: int = 10
    offset: int = 0
    order: Literal["asc", "desc"] = "desc"
    order_by: Literal["created_at", "updated_at"] = "created_at"