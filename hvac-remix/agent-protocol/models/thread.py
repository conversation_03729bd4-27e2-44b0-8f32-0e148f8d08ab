"""
Thread models for Agent Protocol.
"""

from typing import Dict, List, Optional, Any, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID, uuid4


class Thread(BaseModel):
    """Thread model."""
    
    thread_id: str = Field(default_factory=lambda: str(uuid4()))
    status: Literal["active", "completed", "error", "cancelled"] = "active"
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    values: Dict[str, Any] = Field(default_factory=dict)
    messages: List[Dict[str, Any]] = Field(default_factory=list)


class ThreadCreate(BaseModel):
    """Thread creation model."""
    
    thread_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ThreadUpdate(BaseModel):
    """Thread update model."""
    
    metadata: Optional[Dict[str, Any]] = None
    values: Optional[Dict[str, Any]] = None


class ThreadSearchParams(BaseModel):
    """Thread search parameters."""
    
    metadata: Optional[Dict[str, Any]] = None
    limit: int = 10
    offset: int = 0
    order: Literal["asc", "desc"] = "desc"
    order_by: Literal["created_at", "updated_at"] = "created_at"