"""
Run models for Agent Protocol.
"""

from typing import Dict, List, Optional, Any, Literal, Union
from datetime import datetime
from pydantic import BaseModel, Field
from uuid import UUID, uuid4


class Run(BaseModel):
    """Run model."""
    
    run_id: str = Field(default_factory=lambda: str(uuid4()))
    thread_id: str
    agent_id: str
    status: Literal["pending", "running", "completed", "failed", "cancelled"] = "pending"
    input: Dict[str, Any] = Field(default_factory=dict)
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class RunCreate(BaseModel):
    """Run creation model."""
    
    thread_id: str
    agent_id: Optional[str] = None
    input: Dict[str, Any]
    metadata: Dict[str, Any] = Field(default_factory=dict)


class RunUpdate(BaseModel):
    """Run update model."""
    
    status: Optional[Literal["pending", "running", "completed", "failed", "cancelled"]] = None
    output: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class RunSearchParams(BaseModel):
    """Run search parameters."""
    
    thread_id: Optional[str] = None
    agent_id: Optional[str] = None
    status: Optional[Literal["pending", "running", "completed", "failed", "cancelled"]] = None
    metadata: Optional[Dict[str, Any]] = None
    limit: int = 10
    offset: int = 0
    order: Literal["asc", "desc"] = "desc"
    order_by: Literal["created_at", "updated_at", "started_at", "completed_at"] = "created_at"