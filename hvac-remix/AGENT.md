# HVAC-CRM Agent Guidelines
Main file project in : /root/hvac-crm/hvac-remix
## Build/Test Commands
- JS/Node: `npm run test` (single test: `npm test -- -t "test name"`)
- Python: `poetry run pytest` (single test: `poetry run pytest path/to/test.py::test_name`)
- Docker: `docker-compose up -d` (specific service: `docker-compose up -d service-name`)

## Code Style Guidelines
- **JS/TS**: Use ES modules (`import/export`), async/await for async operations
- **Python**: Follow PEP 8, use type hints, organize imports alphabetically
- **Error Handling**: Use try/catch blocks with specific error types, proper logging
- **Naming**: camelCase for JS variables/functions, PascalCase for classes
- **Documentation**: JSDoc for JS, docstrings for Python functions
- **API Design**: RESTful endpoints with proper status codes and error responses

## Environment Configuration
- Use `.env` files for configuration (see .env.example files)
- Access env vars via `process.env` (JS) or `os.environ` (Python)
